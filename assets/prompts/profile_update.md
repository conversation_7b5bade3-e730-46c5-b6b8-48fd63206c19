# User Profile Update Analysis

Analyze this conversation and extract relevant information that could enhance the user's profile for better AI coaching personalization.

## Analysis Focus Areas

### Personal Information
- **Goals**: New goals mentioned, goal progress updates, completed goals
- **Interests & Preferences**: Hobbies, activities, likes/dislikes mentioned
- **Personality Traits**: Observable characteristics, communication style, values
- **Life Context**: Family updates, location changes, career information
- **Challenges**: Current obstacles, recurring issues, areas of struggle

### Coaching Insights
- **Communication Preferences**: How the user likes to receive feedback
- **Motivation Patterns**: What energizes or discourages the user
- **Learning Style**: How the user processes information and advice
- **Progress Indicators**: Metrics or signs of growth the user values

## Extraction Guidelines

- **Accuracy**: Only extract information explicitly mentioned or clearly implied
- **Relevance**: Focus on information useful for future coaching sessions
- **Respect**: Maintain user privacy and don't infer sensitive details
- **Context**: Consider the conversation context when interpreting statements

## Output Format

Return the extracted information in structured JSON format:

```json
{
  "goals": [
    {
      "description": "Goal description",
      "status": "active|completed|paused",
      "category": "career|health|relationships|personal"
    }
  ],
  "interests": ["interest1", "interest2"],
  "personalityTraits": ["trait1", "trait2"],
  "preferences": {
    "communicationStyle": "direct|supportive|analytical",
    "feedbackPreference": "immediate|gentle|detailed"
  },
  "facts": [
    {
      "key": "Fact category",
      "value": "Fact value"
    }
  ],
  "insights": {
    "motivationFactors": ["factor1", "factor2"],
    "challenges": ["challenge1", "challenge2"]
  }
}
```

## Important Notes

- Only include information that was explicitly shared in the conversation
- Respect user privacy and avoid making assumptions
- Focus on information that will improve future coaching interactions
- If no relevant information is found, return an empty structure
