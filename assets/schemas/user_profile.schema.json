{"$id": "https://example.com/schemas/user_profile.json", "$schema": "http://json-schema.org/draft-07/schema#", "title": "UserProfile", "description": "Structured long‑term memory record for an AI coach user.", "type": "object", "properties": {"userId": {"type": "string", "description": "Unique identifier for the user"}, "name": {"type": "string", "description": "User’s full name"}, "age": {"type": "integer", "minimum": 0, "description": "User’s age in years"}, "gender": {"type": "string", "enum": ["male", "female", "non-binary", "other", "unspecified"], "description": "User’s self‑identified gender"}, "familyStatus": {"type": "string", "enum": ["single", "married", "partnered", "divorced", "widowed", "unspecified"], "description": "User’s marital or family status"}, "family": {"type": "array", "description": "Information about user’s household members and close relations", "items": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the family member or relation"}, "age": {"type": "integer", "minimum": 0}, "relation": {"type": "string", "description": "e.g. 'son', 'daughter', 'spouse', 'mom', 'dog'"}, "otherInfo": {"type": "array", "description": "Additional notes or context about this relation", "items": {"type": "string"}}}, "required": ["name", "age", "relation"]}}, "location": {"type": "object", "description": "Where the user lives", "properties": {"town": {"type": "string"}, "country": {"type": "string"}}, "required": ["country"]}, "facts": {"type": "array", "description": "Immutable or semi‑immutable known facts about the user", "items": {"type": "object", "properties": {"key": {"type": "string"}, "value": {"type": ["string", "number", "boolean"]}}, "required": ["key", "value"]}}, "likes": {"type": "array", "description": "Things the user enjoys", "items": {"type": "string"}}, "dislikes": {"type": "array", "description": "Things the user dislikes", "items": {"type": "string"}}, "preferences": {"type": "object", "description": "User’s ongoing interaction preferences", "additionalProperties": {"type": ["string", "number", "boolean", "object", "array"]}}, "goals": {"type": "array", "description": "Short‑ and long‑term goals the user is working toward", "items": {"type": "object", "properties": {"id": {"type": "string"}, "description": {"type": "string"}, "status": {"type": "string", "enum": ["not_started", "in_progress", "achieved", "abandoned"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "description", "status", "createdAt"]}}, "personalityTraits": {"type": "array", "description": "Traits inferred from conversation (e.g. 'analytical','introverted')", "items": {"type": "string"}}, "interactionHistory": {"type": "object", "description": "Metadata about memory updates", "properties": {"lastUpdated": {"type": "string", "format": "date-time"}, "sources": {"type": "array", "items": {"type": "object", "properties": {"sessionId": {"type": "string"}, "timestamp": {"type": "string", "format": "date-time"}}, "required": ["sessionId", "timestamp"]}}}, "required": ["lastUpdated"]}}, "required": ["userId", "interactionHistory"], "additionalProperties": false}