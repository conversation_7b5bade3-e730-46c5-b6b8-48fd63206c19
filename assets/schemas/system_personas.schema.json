{"$schema": "http://json-schema.org/draft-07/schema#", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "JSON schema for validating SystemPersona data structure", "type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "description": "Firestore document ID (optional for seeding data)"}, "name": {"type": "string", "description": "The name of the persona", "minLength": 1}, "description": {"type": "string", "description": "A detailed description of the persona's coaching style and approach", "minLength": 1}, "avatarUrl": {"type": "string", "description": "URL or path to the persona's avatar image", "minLength": 1}, "isActive": {"type": "boolean", "description": "Whether the persona is active and available for selection"}, "approach": {"type": "string", "description": "The persona's general approach to coaching", "minLength": 1}, "coachingStyle": {"type": "string", "description": "The specific coaching style of the persona", "minLength": 1}, "specialties": {"type": "array", "description": "List of the persona's specialties and areas of expertise", "items": {"type": "string", "minLength": 1}, "minItems": 1, "uniqueItems": true}, "videoUrl": {"type": "string", "description": "URL or path to the persona's introduction video", "minLength": 1}}, "required": ["name", "description", "avatarUrl", "isActive", "approach", "coachingStyle", "specialties", "videoUrl"], "additionalProperties": false}, "minItems": 1}