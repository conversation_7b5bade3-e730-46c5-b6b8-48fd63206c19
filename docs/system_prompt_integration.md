# System Prompt Integration with UserProfile

## Overview

This implementation adds comprehensive UserProfile integration to the Gemini chat functionality, allowing the AI to have personalized context about each user without cluttering the visible conversation history.

## Key Features

- **System Instructions**: Uses Gemini's `Content.system()` to provide persistent context that doesn't appear in chat history
- **UserProfile Integration**: Automatically fetches and incorporates user profile data into AI context
- **Comprehensive Context**: Includes user demographics, preferences, goals, personality traits, and interaction history
- **Fallback Handling**: Gracefully handles cases where UserProfile data is unavailable
- **Structured Prompts**: Uses a dedicated builder class for consistent, maintainable system prompt generation

## Implementation Details

### Files Modified/Created

1. **`lib/services/firestore.dart`**

   - Added `getUserProfile(String userId)` method
   - Added `createOrUpdateUserProfile(String userId, UserProfile profile)` method
   - Follows existing service patterns for consistency

2. **`lib/utils/system_prompt_builder.dart`** (NEW)

   - `SystemPromptBuilder.buildSystemPrompt()` static method
   - Generates comprehensive system prompts with user context
   - Includes AI role definition, timestamps, user profile data, and objectives

3. **`lib/pages/chat_page.dart`**

   - Modified to fetch UserProfile during initialization
   - Configures Gemini model with system instructions
   - Maintains fallback initialization if UserProfile loading fails

4. **`test/utils/system_prompt_builder_test.dart`** (NEW)
   - Comprehensive test suite for SystemPromptBuilder
   - Tests various scenarios including full and minimal UserProfile data

### System Prompt Structure

The generated system prompt includes:

1. **AI Agent Role Definition**

   - Professional coaching persona
   - Communication style guidelines
   - Response formatting instructions

2. **Current Context**

   - Current timestamp and time context
   - Session objectives (if provided)
   - Additional instructions (if provided)

3. **User Profile Information** (when available)

   - Demographics (name, age, gender, location)
   - Personal details (family status, relationships)
   - Preferences and personality traits
   - Goals and aspirations
   - Likes, dislikes, and facts
   - Interaction history summary

4. **Response Guidelines**
   - Personalization instructions
   - Tone and style preferences
   - Goal-oriented coaching approach

### Example Usage

```dart
// In ChatPage initialization
final userProfile = await FirestoreService.getUserProfile(userId);
final systemPrompt = SystemPromptBuilder.buildSystemPrompt(
  userProfile: userProfile,
  currentTime: DateTime.now(),
  chatObjective: 'Provide personalized career coaching',
  additionalInstructions: ['Focus on actionable steps'],
);

// Configure Gemini model with system instructions
_model = FirebaseAI.googleAI().generativeModel(
  model: 'gemini-2.5-flash',
  systemInstruction: Content.system(systemPrompt),
);
```

## Benefits

1. **Personalized Responses**: AI has rich context about the user's background, goals, and preferences
2. **Clean Chat History**: System context doesn't clutter the visible conversation
3. **Consistent Context**: User information persists across all messages in the session
4. **Scalable Architecture**: Easy to extend with additional user context
5. **Maintainable Code**: Dedicated builder class for system prompt generation

## Testing

All tests pass successfully:

- Basic system prompt generation without user profile
- Comprehensive system prompt with full UserProfile data
- Handling of minimal UserProfile information
- Proper fallback behavior

## Next Steps

The implementation is complete and ready for use. The system will automatically:

1. Fetch UserProfile data when initializing chat sessions
2. Generate appropriate system prompts based on available data
3. Configure the Gemini model with personalized context
4. Provide fallback initialization if UserProfile data is unavailable

This creates a foundation for highly personalized AI coaching interactions while maintaining clean separation between system context and user-visible conversation history.
