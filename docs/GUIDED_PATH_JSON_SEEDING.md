# Guided Path JSON Seeding System

This document describes the new JSON-based guided path seeding system for the Upshift application.

## Overview

The Upshift guided paths seeding system has been enhanced with a new JSON-based approach that separates data from code, making it easier to maintain, validate, and update guided path content without modifying Dart source files.

## Files Created

### 1. Data File
- **`data/guide-paths.json`** - Contains all guided path and step data in structured JSON format

### 2. Schema File
- **`assets/schemas/guided_paths.schema.json`** - JSON Schema for validating the structure of guide-paths.json

### 3. Seeder Class
- **`lib/utils/admin/guided_path_seeder_from_json.dart`** - New seeder class that reads from JSON instead of hardcoded data

### 4. Tests
- **`test/utils/guided_path_seeder_from_json_test.dart`** - Unit tests for the JSON-based seeder

### 5. Example
- **`example/guided_path_seeder_usage.dart`** - Example showing how to use and compare both seeders

### 6. Documentation
- **Updated `README.md`** - Added schema validation instructions

## Key Features

### JSON Data Structure

The `guide-paths.json` file contains an array of objects, each with:
- **`path`** - GuidedPath object data
- **`steps`** - Array of PathStep objects

```json
[
  {
    "path": {
      "name": "Zero-to-Flow",
      "category": "Focus & Productivity",
      "description": "Transform scattered attention...",
      "stepCount": 6,
      "targetUserTier": "free",
      // ... other GuidedPath fields
    },
    "steps": [
      {
        "stepNumber": 1,
        "title": "Goal Setting Foundation",
        "description": "Define clear, actionable goals...",
        // ... other PathStep fields
      }
      // ... more steps
    ]
  }
  // ... more paths
]
```

### Schema Validation

The JSON schema provides:
- **Structure validation** - Ensures required fields are present
- **Type checking** - Validates data types (string, number, boolean, array)
- **Enum validation** - Validates category, tier, and difficulty values
- **Constraint validation** - Ensures minimum values, array lengths, etc.

### API Compatibility

The new `GuidedPathSeederFromJson` class maintains the same public interface as the original `GuidedPathSeeder`:

```dart
// Both classes support these methods:
static Future<List<Map<String, dynamic>>> getDefaultPathsWithSteps()
static Future<Map<String, List<String>>> seedDefaultPathsWithSteps()
static Future<bool> pathsExist()
static Future<Map<String, List<String>>> seedIfEmpty()
static Future<List<String>> getDefaultCategories()
static Future<String?> getStarterPathName()
```

## Usage

### Basic Usage

```dart
import 'package:upshift/utils/admin/guided_path_seeder_from_json.dart';

// Load paths from JSON
final pathsWithSteps = await GuidedPathSeederFromJson.getDefaultPathsWithSteps();

// Seed to Firestore (only if empty)
final result = await GuidedPathSeederFromJson.seedIfEmpty();

// Get categories
final categories = await GuidedPathSeederFromJson.getDefaultCategories();
```

### Validation

```dart
// Validate JSON structure
final isValid = await GuidedPathSeederFromJson.validateJsonStructure();
```

### Schema Validation (Command Line)

```bash
# Install validator
npm install -g ajv-cli

# Validate JSON against schema
ajv validate -s assets/schemas/guided_paths.schema.json -d data/guide-paths.json
```

## Benefits

### 1. Separation of Concerns
- **Data** is stored in JSON files
- **Logic** remains in Dart classes
- **Schema** provides validation rules

### 2. Easier Maintenance
- Content updates don't require code changes
- No need to recompile when updating path content
- Version control shows clear content changes

### 3. Validation & Quality Assurance
- JSON schema ensures data consistency
- Automated validation in CI/CD pipelines
- Clear error messages for invalid data

### 4. Flexibility
- Easy to add new paths without code changes
- Simple to modify existing content
- Support for different data sources (JSON, API, etc.)

## Migration Path

The new JSON-based seeder can be used as a drop-in replacement:

```dart
// Old approach
import 'package:upshift/utils/admin/guided_path_seeder.dart';
final result = await GuidedPathSeeder.seedIfEmpty();

// New approach
import 'package:upshift/utils/admin/guided_path_seeder_from_json.dart';
final result = await GuidedPathSeederFromJson.seedIfEmpty();
```

## Data Included

The JSON file includes all 4 guided paths:

1. **Zero-to-Flow** (Focus & Productivity, Free tier)
   - 6 steps covering goal setting, time-blocking, distraction audit, Pomodoro technique, reflection, and habit sustainability

2. **Mind Armor** (Mindset & Resilience, Paid tier)
   - 6 steps covering limiting beliefs, cognitive reframing, micro-wins, stress toolkit, affirmations, and growth mindset

3. **Habit Forge** (Habit Formation, Paid tier)
   - 6 steps covering trigger selection, habit loops, micro trials, tracking, accountability, and habit stacking

4. **Vision Blueprint** (Life Design, Paid tier)
   - 6 steps covering values clarification, vision creation, backcasting, milestone planning, roadmap creation, and review systems

## Future Enhancements

Potential improvements for the JSON-based system:

1. **Dynamic Loading** - Load paths from remote APIs
2. **Localization** - Support for multiple languages
3. **A/B Testing** - Different path variations
4. **User Customization** - Personalized path modifications
5. **Analytics Integration** - Track path effectiveness

## Testing

The system includes comprehensive tests:
- JSON loading and parsing
- Data structure validation
- API compatibility verification
- Error handling

Run tests with:
```bash
flutter test test/utils/guided_path_seeder_from_json_test.dart
```

## Conclusion

The JSON-based guided path seeding system provides a more maintainable, flexible, and robust approach to managing guided path content in the Upshift application while maintaining full backward compatibility with the existing codebase.
