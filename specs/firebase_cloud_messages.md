# Firebase Cloud Messaging (FCM) Payload Structure for Upshift

The key to a good solution is to use a flexible data structure, like a JSON object, that can accommodate different message types while still being easy to parse in your Flutter app.

---

### Option 1: Polymorphic `data` Field

This approach uses a generic `data` field in the top-level notification payload. The contents of this `data` field change based on a `messageType` field.

**Engagement Example:**

```json
{
  "messageType": "engagement",
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
    "title": "Don't Forget to Check In!",
    "description": "See what you missed this week.",
    "imageUrl": "https://example.com/images/weekly_update.png"
  }
}
```

**Goals Progress Tracking Example:**

```json
{
  "messageType": "goals_and_progress",
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
    "goalName": "Complete your fitness challenge",
    "goalId": "goal_123",
    "descriptiopn": "You're 75% of the way there!",
    "progressPercentage": 75,
    "currentStep": "Day 5 workout"
  }
}
```

**Guided Path Example:**

```json
{
  "messageType": "guided_path_updates",
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
    "pathId": "path_123",
    "pathName": "Beginner's Meditation",
    "stepId": "step_456",
    "stepName": "Breathing Exercises",
    "imageUrl": "https://example.com/images/meditation_step.png"
  }
}
```

**System Announcements Example:**

```json
{
  "messageType": "system_announcements",
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
    "announcementId": "announcement_123",
    "announcementTitle": "New Feature Launch!",
    "announcementBody": "Check out our latest updates...",
    "imageUrl": "https://example.com/images/feature_launch.png"
  }
}
```

**Weekly Insights Example:**

```json
{
  "messageType": "weekly_insights",
  "notification": {
    "title": "...",
    "body": "..."
  },
  "data": {
    "insightId": "insight_123",
    "insightTitle": "Your Weekly Progress",
    "insightBody": "Here's what you achieved this week...",
    "imageUrl": "https://example.com/images/weekly_insight.png"
  }
}
```

#### Pros

- **Highly Flexible:** You can easily add new message types with their own unique data structures without changing the top-level payload.
- **Clear Separation:** The `notification` field handles the display aspects (title, body), while the `data` field is for custom, app-specific logic.
- **Type-Safe:** In Flutter, you can use a `switch` statement on `messageType` to parse the `data` field into a specific Dart object (e.g., `EngagementMessage`, `ProgressMessage`).

#### Cons

- Requires a bit more initial setup on the Flutter side to handle the different `data` structures.

### Recommendation

The **polymorphic `data` field (Option 1)** is the most extensible and maintainable solution. It perfectly balances flexibility and structure. By using a `messageType` field to guide the parsing of the `data` payload, you're building a system that can grow with your application without becoming a tangled mess.

Here's a simple example of what the Dart model and parsing logic could look like for Option 1:

```dart
// The base class for all your custom notification data
abstract class PushNotificationData {}

// Specific data models for each message type
class EngagementData extends PushNotificationData {
  final String title;
  final String description;
  final String? imageUrl;

  EngagementData({required this.title, required this.description, this.imageUrl});
  // Add a factory constructor to parse JSON
  factory EngagementData.fromJson(Map<String, dynamic> json) { ... }
}

class GuidedPathData extends PushNotificationData {
  final String pathId;
  final String pathName;
  final String stepId;
  final String stepName;
  final String? imageUrl;

  GuidedPathData({
    required this.pathId,
    required this.pathName,
    required this.stepId,
    required this.stepName,
    this.imageUrl
  });
  // Add a factory constructor to parse JSON
  factory GuidedPathData.fromJson(Map<String, dynamic> json) { ... }
}

// Main function to parse the incoming push notification
PushNotificationData? parseNotificationData(Map<String, dynamic> message) {
  final messageType = message['messageType'] as String?;
  final data = message['data'] as Map<String, dynamic>?;

  if (messageType == null || data == null) {
    return null;
  }

  switch (messageType) {
    case 'engagement':
      return EngagementData.fromJson(data);
    case 'guided_path_continue':
      return GuidedPathData.fromJson(data);
    // Add more cases for other message types
    default:
      return null;
  }
}
```

This approach creates a clear, maintainable, and scalable architecture for handling all your push notification needs.
