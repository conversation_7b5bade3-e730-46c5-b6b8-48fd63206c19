# Firebase Logging and Monitoring Plan for Upshift

## I. Core Principles for Logging and Monitoring with Firebase

- **Centralized Error Reporting:** All errors (fatal and non-fatal) should be captured and reported to a central service for analysis.
- **User Behavior Tracking:** Understand how users interact with your app, identify popular features, and discover potential pain points.
- **Performance Measurement:** Monitor app startup, network requests, and custom code execution to identify performance bottlenecks.
- **Remote Configuration & A/B Testing:** Dynamically adjust app behavior and test different features/UI without app updates.
- **Data-Driven Decisions:** Use collected data to make informed decisions about app improvements and marketing strategies.

## II. Firebase Services for Logging and Monitoring

### 1\. Firebase Crashlytics (for Error Logging and Crash Reporting)

Crashlytics is the primary tool for capturing and analyzing crashes and non-fatal errors.

**Implementation Steps:**

- **Add Dependency:**
  ```yaml
  flutter pub add firebase_crashlytics
  ```
- **Initialize Firebase and Crashlytics:**
  Ensure `WidgetsFlutterBinding.ensureInitialized()` and `Firebase.initializeApp()` are called before `runApp()`.

  ```dart
  import 'package:firebase_core/firebase_core.dart';
  import 'package:firebase_crashlytics/firebase_crashlytics.dart';
  import 'package:flutter/foundation.dart' show kDebugMode;
  import 'package:flutter/widgets.dart';

  void main() async {
    WidgetsFlutterBinding.ensureInitialized();
    await Firebase.initializeApp();

    // Pass all uncaught errors from the Flutter framework to Crashlytics.
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;

    // Catch errors happening outside of the Flutter framework (e.g., in async operations)
    runZonedGuarded<Future<void>>(() async {
      runApp(const MyApp());
    }, (error, stack) => FirebaseCrashlytics.instance.recordError(error, stack));

    if (kDebugMode) {
      // Force disable Crashlytics collection while doing everyday development.
      // Temporarily toggle this to true if you want to test crash reporting in your app.
      await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(false);
    } else {
      // Handle Crashlytics enabled status when not in Debug,
      // e.g. allow your users to opt-in to crash reporting.
      await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
    }
  }
  ```

- **Manually Log Non-Fatal Errors:**
  For errors that don't crash the app but indicate a problem, use `recordError`:
  ```dart
  try {
    // Some potentially problematic code
  } catch (e, s) {
    FirebaseCrashlytics.instance.recordError(e, s, reason: 'Non-fatal error in data processing');
  }
  ```
- **Add Custom Information to Crash Reports:**
  - **Logs:** Add "breadcrumb" logs to understand the user's journey leading to a crash. These appear in Crashlytics reports.
    ```dart
    FirebaseCrashlytics.instance.log('User navigated to product details screen');
    FirebaseCrashlytics.instance.log('Attempting to fetch product data for ID: 123');
    ```
  - **Keys:** Set key-value pairs that are attached to crash reports.
    ```dart
    FirebaseCrashlytics.instance.setCustomKey('user_id', 'abc-123');
    FirebaseCrashlytics.instance.setCustomKey('app_version', '1.0.0');
    ```
  - **User Identifiers:** Identify specific users experiencing crashes (e.g., their Firebase Auth UID).
    ```dart
    FirebaseCrashlytics.instance.setUserIdentifier('user_firebase_uid');
    ```
- **Testing Crashlytics:**
  Force a crash to verify integration:
  ```dart
  FirebaseCrashlytics.instance.crash(); // Will immediately exit the app
  ```
  For a non-fatal test:
  ```dart
  await FirebaseCrashlytics.instance.recordError(
    FlutterErrorDetails(exception: 'This is a test non-fatal error'), StackTrace.current
  );
  ```

### 2\. Google Analytics for Firebase (for Event Logging and User Behavior)

Firebase Analytics helps understand user behavior, engagement, and conversion.

**Implementation Steps:**

- **Add Dependency:**
  ```yaml
  flutter pub add firebase_analytics
  ```
- **Initialize Analytics:** Analytics is usually initialized automatically with `Firebase.initializeApp()`, but you can get an instance:

  ```dart
  import 'package:firebase_analytics/firebase_analytics.dart';

  final FirebaseAnalytics analytics = FirebaseAnalytics.instance;
  ```

- **Automatic Events:** Analytics automatically logs some events (e.g., `app_open`, `first_open`).
- **Log Custom Events:** Track specific user actions important to your app. Use suggested events where possible for better reporting.

  ```dart
  // Example: Logging a custom "search" event
  await analytics.logEvent(
    name: 'search',
    parameters: {
      'search_term': 'Flutter widgets',
      'results_count': 15,
    },
  );

  // Example: Logging a suggested "add_to_cart" event
  await analytics.logAddToCart(
    currency: 'USD',
    value: 19.99,
    items: [
      AnalyticsEventItem(
        itemId: 'item_123',
        itemName: 'Flutter Book',
        itemCategory: 'Books',
        price: 19.99,
        quantity: 1,
      ),
    ],
  );
  ```

- **Set User Properties:** Define attributes about your user segments (e.g., `user_type`, `subscription_status`).
  ```dart
  await analytics.setUserProperty(name: 'user_type', value: 'premium');
  ```
- **Set Screen Views:** Track screen transitions (though Flutter's single view controller on native platforms makes automatic screen view tracking less granular; custom event logging is often preferred).
  ```dart
  await analytics.logScreenView(screenName: 'ProductDetailScreen');
  ```
- **DebugView:** Use DebugView in the Firebase console during development to see events in real-time and verify your analytics implementation.

### 3\. Firebase Performance Monitoring (for App Performance Metrics)

Performance Monitoring helps you understand the performance characteristics of your app, including app startup, network requests, and custom code.

**Implementation Steps:**

- **Add Dependency:**
  ```yaml
  flutter pub add firebase_performance
  ```
- **Initialize Performance Monitoring:**

  ```dart
  import 'package:firebase_performance/firebase_performance.dart';

  final FirebasePerformance performance = FirebasePerformance.instance;
  ```

- **Automatic Tracing:**
  - Automatically collects data for app startup time, and HTTP/S network requests.
  - _Note:_ Automatic screen rendering performance monitoring for individual Flutter screens is not possible due to Flutter's native architecture.
- **Custom Code Traces:** Measure the duration of specific tasks in your app.
  ```dart
  Trace trace = performance.newTrace('load_user_profile_data');
  await trace.start();
  try {
    // Your code to load user profile
    await Future.delayed(Duration(seconds: 2));
    trace.setMetric('data_size_kb', 500); // Add custom metrics
    trace.putAttribute('user_type', 'admin'); // Add custom attributes
  } finally {
    await trace.stop();
  }
  ```
- **HTTP Request Tracing (Manual):** If automatic HTTP request tracing isn't sufficient for certain libraries or scenarios, you can manually instrument them.

  ```dart
  import 'package:http/http.dart' as http;

  Future<void> makeNetworkRequest() async {
    final HttpMetric metric = performance.newHttpMetric('https://api.example.com/data', HttpMethod.Get);
    await metric.start();

    try {
      final response = await http.get(Uri.parse('https://api.example.com/data'));
      metric.responsePayloadSize = response.contentLength;
      metric.httpResponseCode = response.statusCode;
      // You can add more attributes or metrics here
    } catch (e) {
      // Handle error
    } finally {
      await metric.stop();
    }
  }
  ```

- **Monitor in Console:** View performance data, including traces and network requests, in the Firebase Performance dashboard. You can filter data by attributes like app version, country, and device.

### 4\. Firebase Remote Config (for Dynamic Configuration and A/B Testing)

While primarily for dynamic configuration, Remote Config, when combined with Analytics and A/B Testing, becomes a powerful monitoring tool by allowing you to test feature impacts on metrics.

**Implementation Steps:**

- **Add Dependency:**
  ```yaml
  flutter pub add firebase_remote_config
  ```
- **Initialize Remote Config:**

  ```dart
  import 'package:firebase_remote_config/firebase_remote_config.dart';

  Future<void> initializeRemoteConfig() async {
    final remoteConfig = FirebaseRemoteConfig.instance;
    await remoteConfig.setConfigSettings(RemoteConfigSettings(
      fetchTimeout: const Duration(minutes: 1),
      minimumFetchInterval: const Duration(hours: 1), // Adjust as needed
    ));
    await remoteConfig.fetchAndActivate();
  }
  ```

- **Define Parameters in Firebase Console:** Set up parameters (e.g., `feature_flag_new_ui`, `welcome_message`) with default values and values for different conditions/audiences.
- **Retrieve Values in App:**
  ```dart
  bool showNewUI = FirebaseRemoteConfig.instance.getBool('feature_flag_new_ui');
  String welcomeMessage = FirebaseRemoteConfig.instance.getString('welcome_message');
  ```
- **A/B Testing Integration:**
  - **Define Experiment:** In the Firebase console, create an A/B test linked to your Remote Config parameter.
  - **Target Audiences:** Use Analytics audiences to target specific user groups for your A/B tests.
  - **Define Goals:** Link the experiment to Analytics events (e.g., `purchase`, `screen_view`) to measure the impact of different variants.
  - **Monitor Results:** Firebase A/B Testing provides a dashboard to compare the performance of different variants against your defined goals. This helps in understanding which changes lead to better user engagement or business outcomes.

## III. Advanced Logging and Monitoring Considerations

### 1\. Custom Logging Abstraction Layer

- Create a simple logging utility that wraps Firebase Analytics, Crashlytics, and potentially a local logger (like `logger` package or `dart:developer`). This allows you to easily switch or extend logging destinations.
- Example:

  ```dart
  class AppLogger {
    static void logInfo(String tag, String message, {Map<String, dynamic>? params}) {
      FirebaseAnalytics.instance.logEvent(name: tag, parameters: params);
      FirebaseCrashlytics.instance.log('$tag: $message'); // Add to crashlytics logs for context
      debugPrint('INFO [$tag]: $message ${params ?? ''}'); // Local debug log
    }

    static void logWarning(String tag, String message, {Map<String, dynamic>? params}) {
      FirebaseCrashlytics.instance.log('WARNING [$tag]: $message');
      // Potentially send a less severe "warning" event to Analytics if needed
      debugPrint('WARNING [$tag]: $message ${params ?? ''}');
    }

    static void logError(dynamic error, StackTrace stack, String tag, String message, {bool fatal = false}) {
      FirebaseCrashlytics.instance.recordError(error, stack, reason: message, fatal: fatal);
      FirebaseAnalytics.instance.logEvent(name: 'error', parameters: {'tag': tag, 'message': message}); // Log error event
      debugPrint('ERROR [$tag]: $message\nError: $error\nStack: $stack');
    }
  }

  // Usage:
  AppLogger.logInfo('HomeScreen', 'User tapped on settings icon');
  AppLogger.logError(e, s, 'ApiCall', 'Failed to fetch user data');
  ```

### 2\. User Consent and Privacy

- Ensure your app complies with privacy regulations (e.g., GDPR, CCPA).
- Implement mechanisms for users to opt-out of analytics and crash reporting if required. Firebase Crashlytics and Performance Monitoring allow disabling data collection:
  ```dart
  FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(false);
  FirebasePerformance.instance.setPerformanceCollectionEnabled(false);
  ```
  For Analytics, this is typically handled by `FirebaseAnalytics.setAnalyticsCollectionEnabled(false)`.

### 3\. Debugging and Development vs. Production

- **Conditional Logging:** Use `kDebugMode` (from `flutter/foundation.dart`) to enable more verbose logging or disable certain Firebase services (like Crashlytics) during development to avoid cluttering production data.
- **Firebase DebugView:** As mentioned, use DebugView in the Firebase console for real-time analytics event validation during development.

### 4\. Integration with Google Cloud Logging (Optional, for deeper server-side or custom logging)

While Firebase provides excellent client-side logging, for server-side logic (e.g., Cloud Functions) or more granular, custom logging needs, you might consider direct integration with Google Cloud Logging. This typically involves sending logs from your Flutter app (via Cloud Functions or a direct API call if security is handled) to Cloud Logging. However, for most Flutter app needs, the Firebase-provided services are sufficient.

### 5\. Alerting

- **Firebase Alerts:** Configure alerts in the Firebase console for Crashlytics (e.g., new fatal issues, regression in crash-free users), Performance Monitoring (e.g., slow network requests, high startup times), and Analytics (e.g., significant drops in key metrics).
- **Integrate with Third-Party Alerting Tools:** For more advanced alerting workflows, you might pipe Firebase data (e.g., via BigQuery exports from Analytics) to tools like PagerDuty, Slack, or custom alerting systems.

### 6\. Dashboards and Reporting

- **Firebase Console:** The primary hub for all your logging and monitoring data.
  - **Crashlytics:** View crash reports, stack traces, logs, and custom keys. Grouped by issue for easy triage.
  - **Analytics:** Track user behavior, events, funnels, and user properties.
  - **Performance:** Monitor app startup, network requests, and custom traces.
  - **A/B Testing:** Analyze experiment results.
- **BigQuery Integration:** Link your Firebase project to BigQuery to perform advanced custom analysis on your raw Analytics data. This allows for complex queries, joining with other datasets, and building custom dashboards in tools like Looker Studio (formerly Google Data Studio).

## IV. Setup Checklist (Pre-requisites)

1.  **Firebase Project Setup:**
    - Create a Firebase project in the Firebase Console.
    - Enable Google Analytics during project creation (or later via Project Settings \> Integrations).
2.  **FlutterFire CLI:** Install the FlutterFire CLI (`dart pub global activate flutterfire_cli`).
3.  **Configure Flutter App with Firebase:**
    - From your Flutter project directory, run `flutterfire configure`. This command will:
      - Ask you to select platforms (iOS, Android, Web).
      - Create `firebase_options.dart`.
      - Add necessary Gradle plugins for Android (for Crashlytics/Performance Monitoring).
4.  **Add Firebase Core:**
    ```yaml
    flutter pub add firebase_core
    ```
    Then run `flutter pub get`.

By following this comprehensive approach, you can establish a robust logging and monitoring system for your Flutter app, leveraging Firebase's powerful capabilities to gain deep insights into your app's stability, performance, and user engagement.
