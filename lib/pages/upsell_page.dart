import 'package:flutter/material.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:upshift/services/subscription_service.dart';
import 'package:upshift/services/logging_service.dart';
import 'package:upshift/theme/theme.dart';

/// Upsell page for displaying subscription options and handling purchases
///
/// This page displays exactly two subscription options fetched from RevenueCat:
/// - Monthly subscription
/// - Annual (yearly) subscription
///
/// Features:
/// - Consistent Upshift theme with growth/progress design philosophy
/// - Purchase flow with loading states and error handling
/// - SelectableText.rich for error display
/// - Navigation back to previous page after successful purchase
class UpsellPage extends StatefulWidget {
  const UpsellPage({super.key});

  @override
  State<UpsellPage> createState() => _UpsellPageState();
}

class _UpsellPageState extends State<UpsellPage> {
  List<Package> _packages = [];
  bool _isLoading = true;
  bool _isPurchasing = false;
  String? _errorMessage;
  Package? _selectedPackage;

  @override
  void initState() {
    super.initState();
    _loadPackages();
  }

  Future<void> _loadPackages() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final packages = await SubscriptionService.instance
          .getAvailablePackages();

      // Filter for monthly and annual packages
      final filteredPackages = packages.where((package) {
        final identifier = package.identifier.toLowerCase();
        final title = package.storeProduct.title.toLowerCase();
        return identifier.contains('monthly') ||
            identifier.contains('annual') ||
            identifier.contains('yearly') ||
            title.contains('monthly') ||
            title.contains('annual') ||
            title.contains('yearly');
      }).toList();

      // Sort packages: monthly first, then annual
      filteredPackages.sort((a, b) {
        final aIsMonthly =
            a.identifier.toLowerCase().contains('monthly') ||
            a.storeProduct.title.toLowerCase().contains('monthly');
        final bIsMonthly =
            b.identifier.toLowerCase().contains('monthly') ||
            b.storeProduct.title.toLowerCase().contains('monthly');

        if (aIsMonthly && !bIsMonthly) return -1;
        if (!aIsMonthly && bIsMonthly) return 1;
        return 0;
      });

      setState(() {
        _packages = filteredPackages;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage =
            'Failed to load subscription options. Please try again.';
        _isLoading = false;
      });

      LoggingService.instance.logError(
        e,
        StackTrace.current,
        'UpsellPage',
        'Failed to load packages',
      );
    }
  }

  Future<void> _makePurchase(Package package) async {
    try {
      setState(() {
        _isPurchasing = true;
        _errorMessage = null;
      });

      final success = await SubscriptionService.instance.makePurchase(package);

      if (success) {
        // Navigate back after successful purchase
        if (mounted) {
          Navigator.of(context).pop(true);
        }
      } else {
        setState(() {
          _errorMessage = 'Purchase was cancelled or failed. Please try again.';
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'An error occurred during purchase. Please try again.';
      });

      LoggingService.instance.logError(
        e,
        StackTrace.current,
        'UpsellPage',
        'Purchase failed',
      );
    } finally {
      if (mounted) {
        setState(() {
          _isPurchasing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Upgrade to Premium',
          style: context.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: context.colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          icon: Icon(AppIcons.back, color: context.colorScheme.onSurface),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    if (_packages.isEmpty) {
      return _buildEmptyState();
    }

    return _buildSubscriptionOptions();
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: AppColors.primary),
          SizedBox(height: AppDimensions.spacingL),
          Text(
            'Loading subscription options...',
            style: context.textTheme.bodyLarge?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: AppDimensions.paddingL,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(AppIcons.warning, size: 64, color: context.colorScheme.error),
            SizedBox(height: AppDimensions.spacingL),
            SelectableText.rich(
              TextSpan(
                text: _errorMessage ?? 'An error occurred',
                style: context.textTheme.bodyLarge?.copyWith(
                  color: context.colorScheme.error,
                ),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingL),
            ElevatedButton(
              onPressed: _loadPackages,
              child: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: AppDimensions.paddingL,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              AppIcons.info,
              size: 64,
              color: context.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            SizedBox(height: AppDimensions.spacingL),
            Text(
              'No subscription options available',
              style: context.textTheme.headlineSmall,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Please check back later or contact support.',
              style: context.textTheme.bodyLarge?.copyWith(
                color: context.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingL),
            ElevatedButton(
              onPressed: _loadPackages,
              child: const Text('Refresh'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionOptions() {
    return SingleChildScrollView(
      padding: AppDimensions.paddingL,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          _buildHeader(),
          SizedBox(height: AppDimensions.spacingXl),

          // Benefits section
          _buildBenefits(),
          SizedBox(height: AppDimensions.spacingXl),

          // Subscription packages
          _buildPackages(),
          SizedBox(height: AppDimensions.spacingL),

          // Error message if any
          if (_errorMessage != null) ...[
            SelectableText.rich(
              TextSpan(
                text: _errorMessage!,
                style: context.textTheme.bodyMedium?.copyWith(
                  color: context.colorScheme.error,
                ),
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingL),
          ],

          // Purchase button
          _buildPurchaseButton(),
          SizedBox(height: AppDimensions.spacingL),

          // Terms and conditions
          _buildTerms(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Unlock Your Full Potential',
          style: context.textTheme.headlineLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.primary,
          ),
        ),
        SizedBox(height: AppDimensions.spacingM),
        Text(
          'Join thousands of users who have transformed their lives with Upshift Premium.',
          style: context.textTheme.bodyLarge?.copyWith(
            color: context.colorScheme.onSurface.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildBenefits() {
    final benefits = [
      {
        'icon': Icons.task_alt,
        'title': 'All Guided Paths',
        'description': 'Access to all guided paths across 4 categories',
        'color': context.getCategoryColor('Focus & Productivity'),
      },
      {
        'icon': AppIcons.user,
        'title': 'All AI Personalities',
        'description': 'Chat with all 10 AI coaching personalities',
        'color': context.getCategoryColor('Mindset & Resilience'),
      },
      {
        'icon': AppIcons.star,
        'title': 'Advanced Features',
        'description': 'Priority support and exclusive content',
        'color': context.getCategoryColor('Life Design'),
      },
      {
        'icon': AppIcons.progress,
        'title': 'Unlimited Growth',
        'description': 'No limits on your personal development journey',
        'color': context.getCategoryColor('Habit Formation'),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Premium Benefits',
          style: context.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: AppDimensions.spacingL),
        ...benefits.map(
          (benefit) => _buildBenefitItem(
            icon: benefit['icon'] as IconData,
            title: benefit['title'] as String,
            description: benefit['description'] as String,
            color: benefit['color'] as Color,
          ),
        ),
      ],
    );
  }

  Widget _buildBenefitItem({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: AppDimensions.spacingM),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: AppDimensions.borderRadiusM,
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: context.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingXs),
                Text(
                  description,
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: context.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPackages() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Choose Your Plan',
          style: context.textTheme.headlineMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: AppDimensions.spacingL),
        ..._packages.map((package) => _buildPackageCard(package)),
      ],
    );
  }

  Widget _buildPackageCard(Package package) {
    final isSelected = _selectedPackage?.identifier == package.identifier;
    final isMonthly =
        package.identifier.toLowerCase().contains('monthly') ||
        package.storeProduct.title.toLowerCase().contains('monthly');
    final isPopular = !isMonthly; // Annual is popular

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPackage = package;
        });
      },
      child: Container(
        margin: EdgeInsets.only(bottom: AppDimensions.spacingM),
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected
                ? AppColors.primary
                : context.colorScheme.outline.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
          borderRadius: AppDimensions.borderRadiusL,
          color: isSelected
              ? AppColors.primary.withValues(alpha: 0.05)
              : context.colorScheme.surface,
        ),
        child: Stack(
          children: [
            Padding(
              padding: AppDimensions.paddingL,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _getPackageTitle(package),
                              style: context.textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: isSelected ? AppColors.primary : null,
                              ),
                            ),
                            SizedBox(height: AppDimensions.spacingXs),
                            Text(
                              _getPackageDescription(package),
                              style: context.textTheme.bodyMedium?.copyWith(
                                color: context.colorScheme.onSurface.withValues(
                                  alpha: 0.7,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            package.storeProduct.priceString,
                            style: context.textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: isSelected ? AppColors.primary : null,
                            ),
                          ),
                          if (!isMonthly) ...[
                            SizedBox(height: AppDimensions.spacingXs),
                            Text(
                              'Save 20%',
                              style: context.textTheme.bodySmall?.copyWith(
                                color: AppColors.success,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            if (isPopular)
              Positioned(
                top: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppDimensions.spacingM,
                    vertical: AppDimensions.spacingXs,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.only(
                      topRight: AppDimensions.borderRadiusL.topRight,
                      bottomLeft: AppDimensions.borderRadiusM.bottomLeft,
                    ),
                  ),
                  child: Text(
                    'POPULAR',
                    style: context.textTheme.labelSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            if (isSelected)
              Positioned(
                top: AppDimensions.spacingM,
                left: AppDimensions.spacingM,
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.check, color: Colors.white, size: 16),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPurchaseButton() {
    final isEnabled = _selectedPackage != null && !_isPurchasing;

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: isEnabled ? () => _makePurchase(_selectedPackage!) : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(vertical: AppDimensions.spacingL),
          shape: RoundedRectangleBorder(
            borderRadius: AppDimensions.borderRadiusL,
          ),
        ),
        child: _isPurchasing
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                _selectedPackage != null
                    ? 'Start Premium - ${_selectedPackage!.storeProduct.priceString}'
                    : 'Select a Plan',
                style: context.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }

  Widget _buildTerms() {
    return Text(
      'By subscribing, you agree to our Terms of Service and Privacy Policy. '
      'Subscription automatically renews unless cancelled at least 24 hours before the end of the current period.',
      style: context.textTheme.bodySmall?.copyWith(
        color: context.colorScheme.onSurface.withValues(alpha: 0.6),
      ),
      textAlign: TextAlign.center,
    );
  }

  String _getPackageTitle(Package package) {
    final identifier = package.identifier.toLowerCase();
    final title = package.storeProduct.title.toLowerCase();

    if (identifier.contains('monthly') || title.contains('monthly')) {
      return 'Monthly Premium';
    } else if (identifier.contains('annual') ||
        identifier.contains('yearly') ||
        title.contains('annual') ||
        title.contains('yearly')) {
      return 'Annual Premium';
    }

    return package.storeProduct.title;
  }

  String _getPackageDescription(Package package) {
    final identifier = package.identifier.toLowerCase();
    final title = package.storeProduct.title.toLowerCase();

    if (identifier.contains('monthly') || title.contains('monthly')) {
      return 'Billed monthly, cancel anytime';
    } else if (identifier.contains('annual') ||
        identifier.contains('yearly') ||
        title.contains('annual') ||
        title.contains('yearly')) {
      return 'Billed annually, best value';
    }

    return 'Premium subscription';
  }
}
