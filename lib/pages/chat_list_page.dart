import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'persona_selection_page.dart';
import '../services/firestore.dart';
import '../models/models.dart';
import '../theme/theme.dart';
import '../widgets/chat_card.dart';

class ChatListPage extends StatefulWidget {
  const ChatListPage({super.key});

  @override
  State<ChatListPage> createState() => _ChatListPageState();
}

class _ChatListPageState extends State<ChatListPage> {
  List<Chat> _chats = [];
  Map<String, SystemPersona> _systemPersonas = {};
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadChats();
  }

  Future<void> _loadChats() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        final allChats = await FirestoreService.getChats(currentUser.uid);

        // Filter for non-archived chats (include both active and completed)
        final chats = allChats
            .where((chat) => (chat.archived != true))
            .toList();

        // Sort by last updated date (most recent first)
        chats.sort((a, b) => b.lastUpdatedDate.compareTo(a.lastUpdatedDate));

        // Load system personas for chats that have them
        await _loadSystemPersonas(chats);

        setState(() {
          _chats = chats;
          _isLoading = false;
        });
      } else {
        setState(() {
          _errorMessage = 'User not authenticated';
          _isLoading = false;
        });
      }
    } catch (e) {
      final errorMsg = 'Failed to load active chats: $e';
      debugPrint('Error loading active chats: $errorMsg');
      setState(() {
        _errorMessage = errorMsg;
        _isLoading = false;
      });
    }
  }

  Future<void> _loadSystemPersonas(List<Chat> chats) async {
    try {
      // Get unique system persona IDs from chats (all chats now have systemPersonaId)
      final personaIds = chats.map((chat) => chat.systemPersonaId).toSet();

      if (personaIds.isNotEmpty) {
        // Load all active system personas
        final allPersonas = await FirestoreService.getActiveSystemPersonas();

        // Create a map of persona ID to persona for quick lookup
        final personaMap = <String, SystemPersona>{};
        for (final persona in allPersonas) {
          if (persona.id != null && personaIds.contains(persona.id)) {
            personaMap[persona.id!] = persona;
          }
        }

        _systemPersonas = personaMap;
      }
    } catch (e) {
      debugPrint('Error loading system personas: $e');
    }
  }

  Future<void> _createNewChat() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(const SnackBar(content: Text('User not authenticated')));
        return;
      }

      // Navigate to PersonaSelectionPage for new chat
      if (mounted) {
        Navigator.of(context)
            .push(
              MaterialPageRoute(
                builder: (context) => const PersonaSelectionPage(),
              ),
            )
            .then((_) {
              // Refresh the chat list when returning from the chat flow
              _loadChats();
            });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to create chat: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chats'),
        actions: [
          IconButton(icon: Icon(AppIcons.refresh), onPressed: _loadChats),
        ],
      ),
      body: _buildBody(),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewChat,
        child: Icon(AppIcons.add),
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              AppIcons.error,
              size: AppDimensions.iconXxl,
              color: context.colorScheme.error,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text('Error', style: context.textTheme.headlineSmall),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              _errorMessage!,
              style: context.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingM),
            ElevatedButton(onPressed: _loadChats, child: const Text('Retry')),
          ],
        ),
      );
    }

    if (_chats.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              AppIcons.chat,
              size: AppDimensions.iconXxl,
              color: context.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text('No active chats', style: context.textTheme.headlineSmall),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Start a new conversation by tapping the + button',
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Padding(
      padding: EdgeInsets.only(
        top: AppDimensions.spacingL,
        bottom: AppDimensions.spacingXl,
      ),
      child: ListView.builder(
        itemCount: _chats.length,
        itemBuilder: (context, index) {
          final chat = _chats[index];
          final systemPersona = _systemPersonas[chat.systemPersonaId];
          return Padding(
            padding: EdgeInsets.only(
              bottom: index == _chats.length - 1
                  ? AppDimensions.spacingXl
                  : AppDimensions.spacingS,
            ),
            child: ChatCard(
              chat: chat,
              systemPersona: systemPersona,
              onRefresh: _loadChats,
            ),
          );
        },
      ),
    );
  }
}
