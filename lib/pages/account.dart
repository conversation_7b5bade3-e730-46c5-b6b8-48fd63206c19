import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter/material.dart';
import '../theme/theme.dart';
import '../services/theme_service.dart';
import '../services/firestore.dart';
import '../services/subscription_service.dart';
import '../models/models.dart';
import 'notifications_page.dart';
import 'privacy_settings.dart';
import 'upsell_page.dart';

class AccountPage extends StatefulWidget {
  const AccountPage({super.key});

  @override
  State<AccountPage> createState() => _AccountPageState();
}

class _AccountPageState extends State<AccountPage> {
  firebase_auth.User? _currentUser;
  UserSubscription? _subscription;
  late final ThemeService _themeService;
  ThemeMode _currentThemeMode = ThemeMode.system;

  @override
  void initState() {
    super.initState();
    _currentUser = firebase_auth.FirebaseAuth.instance.currentUser;
    _themeService = ThemeService.instance;
    _currentThemeMode = _themeService.currentTheme;
    _loadSubscriptionStatus();

    // Listen to theme changes
    _themeService.themeStream.listen((themeMode) {
      if (mounted) {
        setState(() {
          _currentThemeMode = themeMode;
        });
      }
    });

    // Listen to subscription changes
    SubscriptionService.instance.subscriptionStream.listen((subscription) {
      if (mounted) {
        setState(() {
          _subscription = subscription;
        });
      }
    });
  }

  Future<void> _loadSubscriptionStatus() async {
    try {
      if (SubscriptionService.instance.isInitialized) {
        await SubscriptionService.instance.refreshCustomerInfo();
        setState(() {
          _subscription = SubscriptionService.instance.currentSubscription;
        });
      }
    } catch (e) {
      // Handle error silently - subscription status is not critical for account page
    }
  }

  Future<void> _signOut() async {
    try {
      await firebase_auth.FirebaseAuth.instance.signOut();
      // Also log out from subscription service
      if (SubscriptionService.instance.isInitialized) {
        await SubscriptionService.instance.logOut();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to sign out: $e')));
      }
    }
  }

  void _showSignOutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Sign Out'),
          content: const Text('Are you sure you want to sign out?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _signOut();
              },
              child: const Text('Sign Out'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Account'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: _showSignOutDialog,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile Header Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Row(
                  children: [
                    // Avatar
                    CircleAvatar(
                      radius: 40,
                      backgroundColor: Theme.of(
                        context,
                      ).colorScheme.primaryContainer,
                      backgroundImage: _currentUser?.photoURL != null
                          ? NetworkImage(_currentUser!.photoURL!)
                          : null,
                      child: _currentUser?.photoURL == null
                          ? Icon(
                              Icons.person,
                              size: 40,
                              color: Theme.of(
                                context,
                              ).colorScheme.onPrimaryContainer,
                            )
                          : null,
                    ),
                    const SizedBox(width: 16),
                    // User Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _currentUser?.displayName ?? 'User',
                            style: Theme.of(context).textTheme.headlineSmall,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _currentUser?.email ?? 'No email',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurface
                                      .withValues(alpha: 0.7),
                                ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Account Information Section
            Text('Account Information', style: context.textTheme.titleLarge),
            SizedBox(height: AppDimensions.spacingM),
            Card(
              child: Column(
                children: [
                  _buildEditableInfoTile(
                    icon: AppIcons.user,
                    title: 'Display Name',
                    subtitle: _currentUser?.displayName ?? 'Not set',
                    onEdit: _showEditNameDialog,
                  ),
                  const Divider(height: 1),
                  _buildInfoTile(
                    icon: AppIcons.info,
                    title: 'Email',
                    subtitle: _currentUser?.email ?? 'Not set',
                  ),
                  const Divider(height: 1),
                  _buildInfoTile(
                    icon: AppIcons.security,
                    title: 'Email Verified',
                    subtitle: _currentUser?.emailVerified == true
                        ? 'Yes'
                        : 'No',
                  ),
                  const Divider(height: 1),
                  _buildInfoTile(
                    icon: Icons.calendar_today,
                    title: 'Account Created',
                    subtitle: _currentUser?.metadata.creationTime != null
                        ? _formatDate(_currentUser!.metadata.creationTime!)
                        : 'Unknown',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Subscription Section
            _buildSubscriptionSection(),
            const SizedBox(height: 24),

            // Settings Section
            Text('Settings', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 12),
            Card(
              child: Column(
                children: [
                  _buildActionTile(
                    icon: Icons.notifications,
                    title: 'Notifications',
                    subtitle: 'Manage notification preferences',
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const NotificationsPage(),
                        ),
                      );
                    },
                  ),
                  const Divider(height: 1),
                  _buildThemeTile(),
                  const Divider(height: 1),
                  _buildActionTile(
                    icon: Icons.privacy_tip,
                    title: 'Privacy',
                    subtitle: 'Privacy and data settings',
                    onTap: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const PrivacySettingsPage(),
                        ),
                      );
                    },
                  ),
                  const Divider(height: 1),
                  _buildActionTile(
                    icon: Icons.help,
                    title: 'Help & Support',
                    subtitle: 'Get help and contact support',
                    onTap: () {
                      // TODO: Implement help & support
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Help & support coming soon'),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Sign Out Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _showSignOutDialog,
                icon: const Icon(Icons.logout),
                label: const Text('Sign Out'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.errorContainer,
                  foregroundColor: Theme.of(
                    context,
                  ).colorScheme.onErrorContainer,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoTile({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
      title: Text(title),
      subtitle: Text(subtitle),
    );
  }

  Widget _buildEditableInfoTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onEdit,
  }) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: IconButton(
        icon: const Icon(Icons.edit),
        onPressed: onEdit,
        tooltip: 'Edit $title',
      ),
    );
  }

  Widget _buildActionTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).colorScheme.primary),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap,
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Widget _buildThemeTile() {
    return ListTile(
      leading: Icon(
        _themeService.getThemeIcon(_currentThemeMode),
        color: Theme.of(context).colorScheme.primary,
      ),
      title: const Text('Theme'),
      subtitle: Text(_themeService.getThemeDisplayName(_currentThemeMode)),
      trailing: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _themeService.getThemeDisplayName(_currentThemeMode),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          const Icon(Icons.chevron_right),
        ],
      ),
      onTap: _showThemeDialog,
    );
  }

  void _showThemeDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Choose Theme'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: ThemeMode.values.map((themeMode) {
              return RadioListTile<ThemeMode>(
                title: Row(
                  children: [
                    Icon(
                      _themeService.getThemeIcon(themeMode),
                      size: 20,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    const SizedBox(width: 12),
                    Text(_themeService.getThemeDisplayName(themeMode)),
                  ],
                ),
                subtitle: Text(_getThemeDescription(themeMode)),
                value: themeMode,
                groupValue: _currentThemeMode,
                onChanged: (ThemeMode? value) {
                  if (value != null) {
                    _themeService.setTheme(value);
                    Navigator.of(context).pop();
                  }
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  String _getThemeDescription(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Always use light theme';
      case ThemeMode.dark:
        return 'Always use dark theme';
      case ThemeMode.system:
        return 'Follow system setting';
    }
  }

  void _showEditNameDialog() {
    final TextEditingController nameController = TextEditingController(
      text: _currentUser?.displayName ?? '',
    );
    bool isLoading = false;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Edit Display Name'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: 'Display Name',
                      hintText: 'Enter your display name',
                      border: OutlineInputBorder(),
                    ),
                    textCapitalization: TextCapitalization.words,
                    maxLength: 50,
                    enabled: !isLoading,
                  ),
                  if (isLoading)
                    const Padding(
                      padding: EdgeInsets.only(top: 16.0),
                      child: CircularProgressIndicator(),
                    ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: isLoading
                      ? null
                      : () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isLoading
                      ? null
                      : () async {
                          final newName = nameController.text.trim();
                          if (newName.isEmpty) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('Name cannot be empty'),
                              ),
                            );
                            return;
                          }

                          if (newName == _currentUser?.displayName) {
                            Navigator.of(context).pop();
                            return;
                          }

                          setState(() {
                            isLoading = true;
                          });

                          try {
                            await FirestoreService.updateUserName(
                              userId: _currentUser!.uid,
                              newName: newName,
                            );

                            // Refresh the current user to get updated display name
                            await _currentUser!.reload();
                            final updatedUser =
                                firebase_auth.FirebaseAuth.instance.currentUser;

                            if (mounted) {
                              // Update the parent widget's state
                              this.setState(() {
                                _currentUser = updatedUser;
                              });

                              // Update dialog state and close
                              setState(() {
                                isLoading = false;
                              });

                              if (context.mounted) {
                                Navigator.of(context).pop();
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      'Display name updated successfully',
                                    ),
                                  ),
                                );
                              }
                            }
                          } catch (e) {
                            if (mounted) {
                              setState(() {
                                isLoading = false;
                              });

                              if (context.mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Failed to update name: $e'),
                                  ),
                                );
                              }
                            }
                          }
                        },
                  child: const Text('Save'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Widget _buildSubscriptionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Subscription', style: context.textTheme.titleLarge),
        SizedBox(height: AppDimensions.spacingM),
        Card(
          child: _subscription?.hasPremiumAccess == true
              ? _buildPremiumSubscriptionCard()
              : _buildFreeSubscriptionCard(),
        ),
      ],
    );
  }

  Widget _buildPremiumSubscriptionCard() {
    return Padding(
      padding: AppDimensions.paddingL,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.spacingM,
                  vertical: AppDimensions.spacingXs,
                ),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: AppDimensions.borderRadiusM,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(AppIcons.crown, size: 16, color: AppColors.primary),
                    SizedBox(width: AppDimensions.spacingXs),
                    Text(
                      'PREMIUM',
                      style: context.textTheme.labelSmall?.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              Icon(AppIcons.success, color: AppColors.success, size: 20),
            ],
          ),
          SizedBox(height: AppDimensions.spacingM),
          Text(
            'Premium Subscription Active',
            style: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: AppDimensions.spacingXs),
          if (_subscription?.subscriptionType != null) ...[
            Text(
              '${_subscription!.subscriptionType!.toUpperCase()} PLAN',
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            SizedBox(height: AppDimensions.spacingXs),
          ],
          if (_subscription?.expirationDate != null) ...[
            Text(
              'Renews on ${_formatDate(_subscription!.expirationDate!)}',
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
            SizedBox(height: AppDimensions.spacingM),
          ],
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _restorePurchases,
                  child: const Text('Restore Purchases'),
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
              Expanded(
                child: OutlinedButton(
                  onPressed: _manageSubscription,
                  child: const Text('Manage'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFreeSubscriptionCard() {
    return Padding(
      padding: AppDimensions.paddingL,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.spacingM,
                  vertical: AppDimensions.spacingXs,
                ),
                decoration: BoxDecoration(
                  color: context.colorScheme.outline.withValues(alpha: 0.1),
                  borderRadius: AppDimensions.borderRadiusM,
                ),
                child: Text(
                  'FREE',
                  style: context.textTheme.labelSmall?.copyWith(
                    color: context.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.spacingM),
          Text(
            'Free Plan',
            style: context.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: AppDimensions.spacingXs),
          Text(
            'Access to one starter path per category',
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: AppDimensions.spacingL),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _navigateToUpsell,
              icon: Icon(AppIcons.crown),
              label: const Text('Upgrade to Premium'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: AppDimensions.spacingM),
              ),
            ),
          ),
          SizedBox(height: AppDimensions.spacingM),
          Center(
            child: TextButton(
              onPressed: _restorePurchases,
              child: const Text('Restore Purchases'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _navigateToUpsell() async {
    final result = await Navigator.of(
      context,
    ).push<bool>(MaterialPageRoute(builder: (context) => const UpsellPage()));

    // If purchase was successful, refresh subscription status
    if (result == true) {
      await _loadSubscriptionStatus();
    }
  }

  Future<void> _restorePurchases() async {
    try {
      if (!SubscriptionService.instance.isInitialized) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Subscription service not available')),
        );
        return;
      }

      final success = await SubscriptionService.instance.restorePurchases();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success
                  ? 'Purchases restored successfully'
                  : 'No purchases found to restore',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to restore purchases')),
        );
      }
    }
  }

  void _manageSubscription() {
    // TODO: Open platform-specific subscription management
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text(
          'Please manage your subscription through the App Store or Google Play Store',
        ),
      ),
    );
  }
}
