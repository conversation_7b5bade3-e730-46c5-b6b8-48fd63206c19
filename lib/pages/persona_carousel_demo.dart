import 'package:flutter/material.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/theme/theme.dart';
import 'package:upshift/widgets/persona_selection_carousel.dart';

/// Demo page showcasing the PersonaSelectionCarousel widget
/// 
/// This page demonstrates:
/// - Horizontal scrolling carousel with PersonaCard widgets
/// - Video playback on avatar tap
/// - Selection state management
/// - Responsive design
class PersonaCarouselDemo extends StatefulWidget {
  const PersonaCarouselDemo({super.key});

  @override
  State<PersonaCarouselDemo> createState() => _PersonaCarouselDemoState();
}

class _PersonaCarouselDemoState extends State<PersonaCarouselDemo> {
  String? _selectedPersonaId;
  
  // Sample personas for demo
  final List<models.SystemPersona> _demoPersonas = [
    models.SystemPersona(
      id: '1',
      name: 'The Motivator',
      description: 'An uplifting and encouraging coach who believes in your potential and helps you see the bright side of every challenge.',
      avatarUrl: 'assets/persona-profile-icon.jpg',
      isActive: true,
    ),
    models.SystemPersona(
      id: '2',
      name: 'The Strategist',
      description: 'A logical and analytical coach who helps you break down complex problems into manageable steps.',
      avatarUrl: 'assets/persona-profile-icon.jpg',
      isActive: true,
    ),
    models.SystemPersona(
      id: '3',
      name: 'The Saint',
      description: 'A compassionate and nurturing coach who provides emotional support and gentle guidance.',
      avatarUrl: 'assets/persona-profile-icon.jpg',
      isActive: true,
    ),
    models.SystemPersona(
      id: '4',
      name: 'The Warrior',
      description: 'A fierce and determined coach who instills discipline and mental toughness.',
      avatarUrl: 'assets/persona-profile-icon.jpg',
      isActive: true,
    ),
    models.SystemPersona(
      id: '5',
      name: 'The Catalyst',
      description: 'An energetic and transformative coach who accelerates change and breakthrough moments.',
      avatarUrl: 'assets/persona-profile-icon.jpg',
      isActive: true,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Persona Carousel Demo'),
        backgroundColor: context.colorScheme.surface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title and description
            Text(
              'Choose Your AI Coach',
              style: context.textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: context.colorScheme.primary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Swipe through our collection of AI coaching personalities. Tap on any avatar to watch their introduction video.',
              style: context.textTheme.bodyLarge?.copyWith(
                color: context.colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
            
            SizedBox(height: AppDimensions.spacingL),
            
            // Persona carousel
            PersonaSelectionCarousel(
              personas: _demoPersonas,
              selectedPersonaId: _selectedPersonaId,
              onPersonaSelected: (personaId) {
                setState(() {
                  _selectedPersonaId = personaId;
                });
              },
              onAvatarTap: (persona) {
                _showPersonaInfo(persona);
              },
              showSelectionIndicator: true,
            ),
            
            SizedBox(height: AppDimensions.spacingL),
            
            // Selected persona info
            if (_selectedPersonaId != null) ...[
              _buildSelectedPersonaInfo(),
              SizedBox(height: AppDimensions.spacingL),
            ],
            
            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedPersonaInfo() {
    final selectedPersona = _demoPersonas.firstWhere(
      (persona) => persona.id == _selectedPersonaId,
    );

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.spacingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: context.colorScheme.primary,
                  size: 24,
                ),
                SizedBox(width: AppDimensions.spacingS),
                Text(
                  'Selected Coach',
                  style: context.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: context.colorScheme.primary,
                  ),
                ),
              ],
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              selectedPersona.name,
              style: context.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              selectedPersona.description ?? 'No description available',
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _selectedPersonaId != null ? _startDemo : null,
            icon: const Icon(Icons.chat),
            label: const Text('Start Demo Chat'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(
                vertical: AppDimensions.spacingM,
              ),
            ),
          ),
        ),
        SizedBox(height: AppDimensions.spacingM),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _resetSelection,
            icon: const Icon(Icons.refresh),
            label: const Text('Reset Selection'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(
                vertical: AppDimensions.spacingM,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _showPersonaInfo(models.SystemPersona persona) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: context.colorScheme.primaryContainer,
              backgroundImage: persona.avatarUrl != null
                  ? AssetImage(persona.avatarUrl!)
                  : null,
              child: persona.avatarUrl == null
                  ? Icon(
                      AppIcons.profile,
                      color: context.colorScheme.onPrimaryContainer,
                    )
                  : null,
            ),
            SizedBox(width: AppDimensions.spacingM),
            Expanded(child: Text(persona.name)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              persona.description ?? 'No description available',
              style: context.textTheme.bodyMedium,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'In a real app, this would play the persona\'s introduction video.',
              style: context.textTheme.bodySmall?.copyWith(
                fontStyle: FontStyle.italic,
                color: context.colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _selectedPersonaId = persona.id;
              });
            },
            child: const Text('Select'),
          ),
        ],
      ),
    );
  }

  void _startDemo() {
    if (_selectedPersonaId == null) return;
    
    final selectedPersona = _demoPersonas.firstWhere(
      (persona) => persona.id == _selectedPersonaId,
    );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Starting demo chat with ${selectedPersona.name}'),
        backgroundColor: context.colorScheme.primary,
      ),
    );
  }

  void _resetSelection() {
    setState(() {
      _selectedPersonaId = null;
    });
  }
}
