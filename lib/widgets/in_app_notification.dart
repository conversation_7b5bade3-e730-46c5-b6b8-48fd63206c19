import 'package:flutter/material.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import '../theme/theme.dart';

/// Custom in-app notification widget that displays foreground notifications
/// with auto-dismiss functionality and theme compliance
class InAppNotification extends StatefulWidget {
  final RemoteMessage message;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;
  final Duration duration;

  const InAppNotification({
    super.key,
    required this.message,
    this.onTap,
    this.onDismiss,
    this.duration = const Duration(seconds: 4),
  });

  @override
  State<InAppNotification> createState() => _InAppNotificationState();

  /// Show an in-app notification overlay
  static void show(
    BuildContext context, {
    required RemoteMessage message,
    VoidCallback? onTap,
    VoidCallback? onDismiss,
    Duration duration = const Duration(seconds: 4),
  }) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 8,
        left: 16,
        right: 16,
        child: Material(
          color: Colors.transparent,
          child: InAppNotification(
            message: message,
            duration: duration,
            onTap: () {
              overlayEntry.remove();
              onTap?.call();
            },
            onDismiss: () {
              overlayEntry.remove();
              onDismiss?.call();
            },
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // Auto-dismiss after duration
    Future.delayed(duration, () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
        onDismiss?.call();
      }
    });
  }
}

class _InAppNotificationState extends State<InAppNotification>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, -1), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutBack,
          ),
        );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final notification = widget.message.notification;
    if (notification == null) return const SizedBox.shrink();

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: _buildNotificationCard(context, notification),
          ),
        );
      },
    );
  }

  Widget _buildNotificationCard(
    BuildContext context,
    RemoteNotification notification,
  ) {
    return Card(
      elevation: 8,
      shadowColor: context.colorScheme.shadow.withValues(alpha: 0.3),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: AppDimensions.paddingM,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              colors: [
                context.colorScheme.surface,
                context.colorScheme.surface.withValues(alpha: 0.95),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            children: [
              // Notification icon
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: context.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getNotificationIcon(),
                  color: context.colorScheme.primary,
                  size: 24,
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),

              // Notification content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (notification.title?.isNotEmpty == true)
                      Text(
                        notification.title!,
                        style: context.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: context.colorScheme.onSurface,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    if (notification.body?.isNotEmpty == true) ...[
                      if (notification.title?.isNotEmpty == true)
                        SizedBox(height: AppDimensions.spacingXs),
                      Text(
                        notification.body!,
                        style: context.textTheme.bodyMedium?.copyWith(
                          color: context.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),

              // Dismiss button
              IconButton(
                onPressed: widget.onDismiss,
                icon: Icon(
                  Icons.close,
                  color: context.colorScheme.onSurfaceVariant,
                  size: 20,
                ),
                constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                padding: const EdgeInsets.all(4),
                tooltip: 'Dismiss notification',
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getNotificationIcon() {
    final messageType = widget.message.data['messageType'] as String?;

    switch (messageType) {
      case 'engagement':
        return Icons.notifications_active;
      case 'goals_and_progress':
        return Icons.outlined_flag;
      case 'guided_path_updates':
        return Icons.route;
      case 'system_announcements':
        return Icons.announcement;
      case 'weekly_insights':
        return Icons.insights;
      default:
        return Icons.notifications;
    }
  }
}
