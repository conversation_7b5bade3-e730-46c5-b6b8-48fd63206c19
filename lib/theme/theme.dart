/// Comprehensive theme system for the Upshift app
///
/// This barrel file exports all theme-related components:
/// - AppTheme: Main theme configuration
/// - AppColors: Color palette and utilities
/// - AppTypography: Text styles and font system
/// - AppIcons: Icon definitions and utilities
/// - AppDimensions: Spacing, sizing, and layout constants
///
/// Usage:
library;

/// ```dart
/// import 'package:upshift/theme/theme.dart';
///
/// // Use in MaterialApp
/// MaterialApp(
///   theme: AppTheme.lightTheme,
///   darkTheme: AppTheme.darkTheme,
///   // ...
/// )
///
/// // Use colors
/// Container(color: AppColors.primary)
///
/// // Use typography
/// Text('Hello', style: AppTypography.headlineLarge)
///
/// // Use icons
/// Icon(AppIcons.home)
///
/// // Use dimensions
/// Padding(padding: AppDimensions.paddingM)
/// ```

export 'app_theme.dart';
export 'app_colors.dart';
export 'app_typography.dart';
export 'app_icons.dart';
export 'app_dimensions.dart';
export 'theme_extensions.dart';
