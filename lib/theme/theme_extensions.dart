import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_typography.dart';
import 'app_dimensions.dart';
import 'app_icons.dart';

/// Extension methods for easy access to theme components
/// Provides convenient access to custom theme elements through BuildContext
extension ThemeExtensions on BuildContext {
  /// Get the current color scheme
  ColorScheme get colorScheme => Theme.of(this).colorScheme;

  /// Get the current text theme
  TextTheme get textTheme => Theme.of(this).textTheme;

  /// Get responsive padding
  EdgeInsets get responsivePadding => AppDimensions.getResponsivePadding(this);

  /// Get responsive spacing
  double get responsiveSpacing => AppDimensions.getResponsiveSpacing(this);

  /// Check if current theme is dark
  bool get isDarkMode => Theme.of(this).brightness == Brightness.dark;

  /// Get surface color with elevation
  Color surfaceWithElevation(double elevation) {
    return AppColors.getSurfaceWithElevation(colorScheme, elevation);
  }

  /// Get category color
  Color getCategoryColor(String category) {
    return AppColors.getCategoryColor(category);
  }

  /// Get category color with opacity
  Color getCategoryColorWithOpacity(String category, double opacity) {
    return AppColors.getCategoryColorWithOpacity(category, opacity);
  }

  /// Get difficulty color
  Color getDifficultyColor(String difficulty) {
    return AppColors.getDifficultyColor(difficulty);
  }

  /// Get status color
  Color getStatusColor(String status) {
    return AppColors.getStatusColor(status);
  }

  /// Get tier color
  Color getTierColor(String tier) {
    return AppColors.getTierColor(tier);
  }

  /// Get adaptive color based on theme brightness
  Color adaptiveColor(
    Color color, {
    double lightOpacity = 1.0,
    double darkOpacity = 0.8,
  }) {
    return AppColors.adaptiveColor(
      color,
      Theme.of(this).brightness,
      lightOpacity: lightOpacity,
      darkOpacity: darkOpacity,
    );
  }
}

/// Extension methods for TextStyle
extension TextStyleExtensions on TextStyle {
  /// Apply color to text style
  TextStyle withColor(Color color) => AppTypography.withColor(this, color);

  /// Apply opacity to text style
  TextStyle withOpacity(double opacity) =>
      AppTypography.withOpacity(this, opacity);

  /// Apply weight to text style
  TextStyle withWeight(FontWeight weight) =>
      AppTypography.withWeight(this, weight);

  /// Apply size to text style
  TextStyle withSize(double size) => AppTypography.withSize(this, size);

  /// Apply letter spacing to text style
  TextStyle withLetterSpacing(double letterSpacing) =>
      AppTypography.withLetterSpacing(this, letterSpacing);

  /// Apply line height to text style
  TextStyle withLineHeight(double height) =>
      AppTypography.withLineHeight(this, height);

  /// Make text style responsive
  TextStyle responsive(BuildContext context) =>
      AppTypography.responsive(this, context);
}

/// Mixin for widgets that need theme-aware styling
mixin ThemeAware<T extends StatefulWidget> on State<T> {
  /// Get the current color scheme
  ColorScheme get colorScheme => Theme.of(context).colorScheme;

  /// Get the current text theme
  TextTheme get textTheme => Theme.of(context).textTheme;

  /// Check if current theme is dark
  bool get isDarkMode => Theme.of(context).brightness == Brightness.dark;

  /// Get category color
  Color getCategoryColor(String category) => context.getCategoryColor(category);

  /// Get status color
  Color getStatusColor(String status) => context.getStatusColor(status);

  /// Get responsive padding
  EdgeInsets get responsivePadding => context.responsivePadding;

  /// Get responsive spacing
  double get responsiveSpacing => context.responsiveSpacing;
}

/// Helper class for creating theme-aware widgets
class ThemeAwareWidget extends StatelessWidget {
  final Widget Function(
    BuildContext context,
    ColorScheme colorScheme,
    TextTheme textTheme,
  )
  builder;

  const ThemeAwareWidget({super.key, required this.builder});

  @override
  Widget build(BuildContext context) {
    return builder(context, context.colorScheme, context.textTheme);
  }
}

/// Helper class for creating responsive widgets
class ResponsiveWidget extends StatelessWidget {
  final Widget Function(
    BuildContext context,
    bool isMobile,
    bool isTablet,
    bool isDesktop,
  )
  builder;

  const ResponsiveWidget({super.key, required this.builder});

  @override
  Widget build(BuildContext context) {
    return builder(
      context,
      AppDimensions.isMobile(context),
      AppDimensions.isTablet(context),
      AppDimensions.isDesktop(context),
    );
  }
}

/// Helper class for creating category-themed widgets
class CategoryThemedWidget extends StatelessWidget {
  final String category;
  final Widget Function(
    BuildContext context,
    Color categoryColor,
    IconData categoryIcon,
  )
  builder;

  const CategoryThemedWidget({
    super.key,
    required this.category,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return builder(
      context,
      context.getCategoryColor(category),
      AppIcons.getCategoryIcon(category),
    );
  }
}

/// Helper class for creating status-themed widgets
class StatusThemedWidget extends StatelessWidget {
  final String status;
  final Widget Function(
    BuildContext context,
    Color statusColor,
    IconData statusIcon,
  )
  builder;

  const StatusThemedWidget({
    super.key,
    required this.status,
    required this.builder,
  });

  @override
  Widget build(BuildContext context) {
    return builder(
      context,
      context.getStatusColor(status),
      AppIcons.getStatusIcon(status),
    );
  }
}

/// Utility class for common theme-aware decorations
class ThemeDecorations {
  /// Create a card decoration with theme-aware styling
  static BoxDecoration card(BuildContext context, {double? elevation}) {
    return AppDimensions.createElevatedDecoration(
      color: context.colorScheme.surface,
      elevation: elevation ?? AppDimensions.elevationS,
      shadowColor: context.colorScheme.primary,
    );
  }

  /// Create a border decoration with theme-aware styling
  static BoxDecoration border(BuildContext context, {Color? borderColor}) {
    return AppDimensions.createBorderDecoration(
      borderColor: borderColor ?? context.colorScheme.outline,
      backgroundColor: context.colorScheme.surface,
    );
  }

  /// Create a gradient decoration
  static BoxDecoration gradient(
    Gradient gradient, {
    BorderRadius? borderRadius,
  }) {
    return BoxDecoration(
      gradient: gradient,
      borderRadius: borderRadius ?? AppDimensions.borderRadiusM,
    );
  }

  /// Create a category-themed decoration
  static BoxDecoration category(
    BuildContext context,
    String category, {
    double opacity = 0.1,
  }) {
    return BoxDecoration(
      color: context.getCategoryColorWithOpacity(category, opacity),
      borderRadius: AppDimensions.borderRadiusM,
      border: Border.all(color: context.getCategoryColor(category), width: 1),
    );
  }

  /// Create a status-themed decoration
  static BoxDecoration status(
    BuildContext context,
    String status, {
    double opacity = 0.1,
  }) {
    return BoxDecoration(
      color: context.getStatusColor(status).withValues(alpha: opacity),
      borderRadius: AppDimensions.borderRadiusS,
      border: Border.all(color: context.getStatusColor(status), width: 1),
    );
  }
}

/// Utility class for common theme-aware text styles
class ThemeTextStyles {
  /// Get category tag text style
  static TextStyle categoryTag(BuildContext context, String category) {
    return AppTypography.categoryTag.copyWith(
      color: context.getCategoryColor(category),
    );
  }

  /// Get status text style
  static TextStyle status(BuildContext context, String status) {
    return AppTypography.textTheme.labelMedium!.copyWith(
      color: context.getStatusColor(status),
      fontWeight: FontWeight.w600,
    );
  }

  /// Get difficulty text style
  static TextStyle difficulty(BuildContext context, String difficulty) {
    return AppTypography.textTheme.labelSmall!.copyWith(
      color: context.getDifficultyColor(difficulty),
      fontWeight: FontWeight.w600,
    );
  }

  /// Get responsive text style
  static TextStyle responsive(BuildContext context, TextStyle baseStyle) {
    return baseStyle.responsive(context);
  }
}
