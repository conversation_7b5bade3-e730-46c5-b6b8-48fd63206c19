import 'package:flutter/services.dart';
import '../models/models.dart';
import 'logging_service.dart';
import 'remote_config_service.dart';

/// Service for managing system prompts through Firebase Remote Config and local assets
///
/// This service handles:
/// - Firebase Remote Config integration for dynamic prompt updates
/// - Local asset fallback for prompt templates
/// - Prompt template loading and caching
/// - Subscription-tier based prompt selection
/// - Error handling and logging for prompt operations
class SystemPromptService {
  static const String _logTag = 'SystemPromptService';
  static SystemPromptService? _instance;

  bool _isInitialized = false;
  final Map<String, String> _promptCache = {};

  // Remote config keys for system prompts
  static const String _chatWithPersonaKey = 'system_prompt_chat_with_persona';
  static const String _titleGenerationKey = 'system_prompt_title_generation';
  static const String _profileUpdateKey = 'system_prompt_profile_update';

  // Local asset paths for prompt templates
  static const String _assetBasePath = 'assets/prompts/';
  static const String _chatWithPersonaAsset =
      '${_assetBasePath}chat_with_persona.md';
  static const String _titleGenerationAsset =
      '${_assetBasePath}title_generation.md';
  static const String _profileUpdateAsset =
      '${_assetBasePath}profile_update.md';

  /// Private constructor for singleton pattern
  SystemPromptService._();

  /// Get the singleton instance
  static SystemPromptService get instance {
    _instance ??= SystemPromptService._();
    return _instance!;
  }

  /// Initialize the System Prompt service
  /// Must be called before using the service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggingService.instance.logInfo(
        _logTag,
        'Initializing SystemPromptService',
      );

      // Pre-load commonly used prompt templates
      await _preloadPromptTemplates();

      _isInitialized = true;

      LoggingService.instance.logInfo(
        _logTag,
        'SystemPromptService initialized successfully',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Failed to initialize SystemPromptService',
      );
      // Don't rethrow - allow app to continue with fallback prompts
    }
  }

  /// Pre-load commonly used prompt templates into cache
  Future<void> _preloadPromptTemplates() async {
    final templates = [
      _chatWithPersonaAsset,
      _titleGenerationAsset,
      _profileUpdateAsset,
    ];

    for (final templatePath in templates) {
      try {
        final content = await _loadAssetTemplate(templatePath);
        _promptCache[templatePath] = content;

        LoggingService.instance.logInfo(
          _logTag,
          'Pre-loaded template: $templatePath',
        );
      } catch (e) {
        LoggingService.instance.logInfo(
          _logTag,
          'Failed to pre-load template $templatePath: $e',
        );
        // Continue with other templates
      }
    }
  }

  /// Load a prompt template from assets
  Future<String> _loadAssetTemplate(String assetPath) async {
    try {
      return await rootBundle.loadString(assetPath);
    } catch (e) {
      LoggingService.instance.logInfo(
        _logTag,
        'Failed to load asset template $assetPath: $e',
      );
      rethrow;
    }
  }

  /// Get the appropriate prompt template for chat with persona
  Future<String> getChatWithPersonaPrompt({
    UserSubscription? userSubscription,
  }) async {
    try {
      _ensureInitialized();

      // Try to get from remote config first
      String? remotePrompt;
      try {
        // Use the public getAllConfigValues method to check if remote config is available
        final configValues = RemoteConfigService.instance.getAllConfigValues();
        remotePrompt = configValues[_chatWithPersonaKey];
      } catch (e) {
        LoggingService.instance.logInfo(
          _logTag,
          'Failed to get remote prompt for $_chatWithPersonaKey: $e',
        );
      }

      if (remotePrompt != null && remotePrompt.isNotEmpty) {
        LoggingService.instance.logInfo(
          _logTag,
          'Using remote prompt for chat with persona',
        );
        return remotePrompt;
      }

      // Fallback to local asset template
      return await _getLocalPromptTemplate(_chatWithPersonaAsset);
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Error getting chat with persona prompt',
      );
      return _getDefaultChatWithPersonaPrompt();
    }
  }

  /// Get the appropriate prompt template for title generation
  Future<String> getTitleGenerationPrompt({
    UserSubscription? userSubscription,
  }) async {
    try {
      _ensureInitialized();

      // Try to get from remote config first
      String? remotePrompt;
      try {
        // Use the public getAllConfigValues method to check if remote config is available
        final configValues = RemoteConfigService.instance.getAllConfigValues();
        remotePrompt = configValues[_titleGenerationKey];
      } catch (e) {
        LoggingService.instance.logInfo(
          _logTag,
          'Failed to get remote prompt for $_titleGenerationKey: $e',
        );
      }

      if (remotePrompt != null && remotePrompt.isNotEmpty) {
        LoggingService.instance.logInfo(
          _logTag,
          'Using remote prompt for title generation',
        );
        return remotePrompt;
      }

      // Fallback to local asset template
      return await _getLocalPromptTemplate(_titleGenerationAsset);
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Error getting title generation prompt',
      );
      return _getDefaultTitleGenerationPrompt();
    }
  }

  /// Get the appropriate prompt template for profile updates
  Future<String> getProfileUpdatePrompt({
    UserSubscription? userSubscription,
  }) async {
    try {
      _ensureInitialized();

      // Try to get from remote config first
      String? remotePrompt;
      try {
        // Use the public getAllConfigValues method to check if remote config is available
        final configValues = RemoteConfigService.instance.getAllConfigValues();
        remotePrompt = configValues[_profileUpdateKey];
      } catch (e) {
        LoggingService.instance.logInfo(
          _logTag,
          'Failed to get remote prompt for $_profileUpdateKey: $e',
        );
      }

      if (remotePrompt != null && remotePrompt.isNotEmpty) {
        LoggingService.instance.logInfo(
          _logTag,
          'Using remote prompt for profile update',
        );
        return remotePrompt;
      }

      // Fallback to local asset template
      return await _getLocalPromptTemplate(_profileUpdateAsset);
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        _logTag,
        'Error getting profile update prompt',
      );
      return _getDefaultProfileUpdatePrompt();
    }
  }

  /// Get local prompt template with caching
  Future<String> _getLocalPromptTemplate(String assetPath) async {
    // Check cache first
    if (_promptCache.containsKey(assetPath)) {
      LoggingService.instance.logInfo(
        _logTag,
        'Using cached template: $assetPath',
      );
      return _promptCache[assetPath]!;
    }

    // Load from asset and cache
    try {
      final content = await _loadAssetTemplate(assetPath);
      _promptCache[assetPath] = content;

      LoggingService.instance.logInfo(
        _logTag,
        'Loaded and cached template: $assetPath',
      );

      return content;
    } catch (e) {
      LoggingService.instance.logInfo(
        _logTag,
        'Failed to load template $assetPath: $e',
      );
      rethrow;
    }
  }

  /// Ensure the service is initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError(
        'SystemPromptService must be initialized before use. Call initialize() first.',
      );
    }
  }

  /// Default fallback prompt for chat with persona
  String _getDefaultChatWithPersonaPrompt() {
    return '''# AI Coach Role & Identity

You are an AI coaching assistant designed to provide personalized guidance, support, and motivation. Your core characteristics:

- **Empathetic & Understanding**: Listen actively and respond with genuine care
- **Adaptive Communication**: Adjust your style based on user preferences and personality
- **Goal-Oriented**: Help users identify, pursue, and achieve their objectives
- **Evidence-Based**: Provide practical, actionable advice grounded in coaching principles
- **Respectful Boundaries**: Maintain professional coaching relationships
- **Growth-Focused**: Encourage continuous learning and development

You are NOT a therapist or medical professional. For serious mental health or medical concerns, encourage users to seek appropriate professional help.

# Response Guidelines

- **Personalization**: Use the user's information naturally in conversation
- **Tone**: Match the user's communication style and preferences
- **Length**: Provide thoughtful, comprehensive responses without being overwhelming
- **Actionability**: Include specific, practical next steps when appropriate
- **Encouragement**: Acknowledge progress and celebrate achievements
- **Curiosity**: Ask thoughtful follow-up questions to deepen understanding
- **Respect**: Honor the user's values, goals, and boundaries''';
  }

  /// Default fallback prompt for title generation
  String _getDefaultTitleGenerationPrompt() {
    return '''Generate a concise, meaningful title (3-10 words) for this chat conversation based on the main topics and themes discussed. The title should be clear, specific, and capture the essence of the conversation.''';
  }

  /// Default fallback prompt for profile updates
  String _getDefaultProfileUpdatePrompt() {
    return '''Analyze this conversation and extract relevant information that could enhance the user's profile. Focus on goals, preferences, personality traits, interests, and important life details. Return the information in structured JSON format.''';
  }

  /// Clear the prompt cache (useful for testing or forcing reload)
  void clearCache() {
    _promptCache.clear();
    LoggingService.instance.logInfo(_logTag, 'Prompt cache cleared');
  }

  /// Get all cached prompt templates for debugging
  Map<String, String> getCachedPrompts() {
    return Map.unmodifiable(_promptCache);
  }
}
