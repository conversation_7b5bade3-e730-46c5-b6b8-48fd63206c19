import 'package:firebase_performance/firebase_performance.dart';
import 'logging_service.dart';

/// Centralized performance monitoring service that integrates Firebase Performance
/// for comprehensive app performance tracking and optimization.
class PerformanceService {
  static final PerformanceService _instance = PerformanceService._internal();
  static PerformanceService get instance => _instance;

  PerformanceService._internal();

  late final FirebasePerformance _performance;
  bool _isInitialized = false;

  /// Initialize the performance monitoring service
  Future<void> initialize() async {
    if (_isInitialized) return;

    _performance = FirebasePerformance.instance;
    _isInitialized = true;

    LoggingService.instance.logInfo(
      'PerformanceService',
      'Performance monitoring service initialized',
    );
  }

  /// Create a new custom trace for measuring performance
  Trace? newTrace(String traceName) {
    if (!_isInitialized) return null;

    try {
      final trace = _performance.newTrace(traceName);

      LoggingService.instance.logInfo(
        'PerformanceService',
        'Created new trace: $traceName',
      );

      return trace;
    } catch (e, stackTrace) {
      LoggingService.instance.logError(
        e,
        stackTrace,
        'PerformanceService',
        'Failed to create trace: $traceName',
      );
      return null;
    }
  }

  /// Create a new HTTP metric for measuring network performance
  HttpMetric? newHttpMetric(String url, HttpMethod httpMethod) {
    if (!_isInitialized) return null;

    try {
      final metric = _performance.newHttpMetric(url, httpMethod);

      LoggingService.instance.logInfo(
        'PerformanceService',
        'Created new HTTP metric: $url',
      );

      return metric;
    } catch (e, stackTrace) {
      LoggingService.instance.logError(
        e,
        stackTrace,
        'PerformanceService',
        'Failed to create HTTP metric: $url',
      );
      return null;
    }
  }

  /// Convenience method to measure a function's execution time
  Future<T> measureTrace<T>(
    String traceName,
    Future<T> Function() function, {
    Map<String, String>? attributes,
    Map<String, int>? metrics,
  }) async {
    final trace = newTrace(traceName);
    if (trace == null) {
      // If trace creation fails, still execute the function
      return await function();
    }

    try {
      await trace.start();

      // Add custom attributes if provided
      if (attributes != null) {
        for (final entry in attributes.entries) {
          trace.putAttribute(entry.key, entry.value);
        }
      }

      final result = await function();

      // Add custom metrics if provided
      if (metrics != null) {
        for (final entry in metrics.entries) {
          trace.setMetric(entry.key, entry.value);
        }
      }

      return result;
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'PerformanceService',
        'Error during trace execution: $traceName',
      );
      rethrow;
    } finally {
      try {
        await trace.stop();
        LoggingService.instance.logInfo(
          'PerformanceService',
          'Completed trace: $traceName',
        );
      } catch (e) {
        LoggingService.instance.logWarning(
          'PerformanceService',
          'Failed to stop trace: $traceName - $e',
        );
      }
    }
  }

  // === PREDEFINED TRACES FOR COMMON OPERATIONS ===

  /// Measure user profile loading performance
  Future<T> measureUserProfileLoad<T>(Future<T> Function() function) async {
    return measureTrace(
      'user_profile_load',
      function,
      attributes: {'operation_type': 'data_load'},
    );
  }

  /// Measure chat creation performance
  Future<T> measureChatCreation<T>(
    Future<T> Function() function,
    String personaId,
  ) async {
    return measureTrace(
      'chat_creation',
      function,
      attributes: {'operation_type': 'chat_creation', 'persona_id': personaId},
    );
  }

  /// Measure message sending performance
  Future<T> measureMessageSend<T>(
    Future<T> Function() function,
    String messageType,
  ) async {
    return measureTrace(
      'message_send',
      function,
      attributes: {
        'operation_type': 'message_send',
        'message_type': messageType,
      },
    );
  }

  /// Measure AI response generation performance
  Future<T> measureAiResponse<T>(
    Future<T> Function() function,
    String personaId,
  ) async {
    return measureTrace(
      'ai_response_generation',
      function,
      attributes: {'operation_type': 'ai_response', 'persona_id': personaId},
    );
  }

  /// Measure Firestore operations performance
  Future<T> measureFirestoreOperation<T>(
    Future<T> Function() function,
    String operationType,
    String collection,
  ) async {
    return measureTrace(
      'firestore_operation',
      function,
      attributes: {'operation_type': operationType, 'collection': collection},
    );
  }

  /// Measure guided path loading performance
  Future<T> measureGuidedPathLoad<T>(
    Future<T> Function() function,
    String pathId,
  ) async {
    return measureTrace(
      'guided_path_load',
      function,
      attributes: {'operation_type': 'guided_path_load', 'path_id': pathId},
    );
  }

  /// Measure onboarding completion performance
  Future<T> measureOnboardingCompletion<T>(
    Future<T> Function() function,
    int selectedPersonasCount,
  ) async {
    return measureTrace(
      'onboarding_completion',
      function,
      attributes: {'operation_type': 'onboarding'},
      metrics: {'selected_personas_count': selectedPersonasCount},
    );
  }

  /// Measure app startup performance
  Future<T> measureAppStartup<T>(Future<T> Function() function) async {
    return measureTrace(
      'app_startup',
      function,
      attributes: {'operation_type': 'app_initialization'},
    );
  }

  /// Check if the performance service is initialized
  bool get isInitialized => _isInitialized;
}
