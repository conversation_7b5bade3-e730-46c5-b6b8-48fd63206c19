import 'dart:async';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing theme state and persistence
/// 
/// This service handles:
/// - Theme mode persistence using SharedPreferences
/// - Theme change notifications via Stream
/// - Integration with the existing AppTheme system
/// - Support for light, dark, and system themes
class ThemeService {
  static const String _themeKey = 'theme_mode';
  static ThemeService? _instance;
  
  late SharedPreferences _prefs;
  final StreamController<ThemeMode> _themeController = StreamController<ThemeMode>.broadcast();
  ThemeMode _currentTheme = ThemeMode.system;

  /// Private constructor for singleton pattern
  ThemeService._();

  /// Get the singleton instance
  static ThemeService get instance {
    _instance ??= ThemeService._();
    return _instance!;
  }

  /// Initialize the theme service
  /// Must be called before using the service
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadTheme();
  }

  /// Get the current theme mode
  ThemeMode get currentTheme => _currentTheme;

  /// Stream of theme changes
  Stream<ThemeMode> get themeStream => _themeController.stream;

  /// Load theme from shared preferences
  Future<void> _loadTheme() async {
    final themeString = _prefs.getString(_themeKey);
    if (themeString != null) {
      _currentTheme = _parseThemeMode(themeString);
    } else {
      // Default to system theme if no preference is saved
      _currentTheme = ThemeMode.system;
    }
    _themeController.add(_currentTheme);
  }

  /// Set the theme mode and persist it
  Future<void> setTheme(ThemeMode themeMode) async {
    if (_currentTheme == themeMode) return;
    
    _currentTheme = themeMode;
    await _prefs.setString(_themeKey, _themeToString(themeMode));
    _themeController.add(_currentTheme);
  }

  /// Toggle between light and dark themes
  /// If currently on system, switches to light
  Future<void> toggleTheme() async {
    switch (_currentTheme) {
      case ThemeMode.light:
        await setTheme(ThemeMode.dark);
        break;
      case ThemeMode.dark:
        await setTheme(ThemeMode.system);
        break;
      case ThemeMode.system:
        await setTheme(ThemeMode.light);
        break;
    }
  }

  /// Get user-friendly display name for theme mode
  String getThemeDisplayName(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  /// Get appropriate icon for theme mode
  IconData getThemeIcon(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return Icons.light_mode;
      case ThemeMode.dark:
        return Icons.dark_mode;
      case ThemeMode.system:
        return Icons.brightness_auto;
    }
  }

  /// Convert ThemeMode to string for persistence
  String _themeToString(ThemeMode themeMode) {
    switch (themeMode) {
      case ThemeMode.light:
        return 'light';
      case ThemeMode.dark:
        return 'dark';
      case ThemeMode.system:
        return 'system';
    }
  }

  /// Parse string to ThemeMode
  ThemeMode _parseThemeMode(String themeString) {
    switch (themeString) {
      case 'light':
        return ThemeMode.light;
      case 'dark':
        return ThemeMode.dark;
      case 'system':
        return ThemeMode.system;
      default:
        return ThemeMode.system;
    }
  }

  /// Dispose resources
  void dispose() {
    _themeController.close();
  }
}
