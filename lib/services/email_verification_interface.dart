import 'package:firebase_auth/firebase_auth.dart';

/// Abstract interface for email verification operations
/// 
/// This interface allows for dependency injection and easier testing
/// by providing a contract for email verification functionality.
abstract class EmailVerificationInterface {
  /// Send email verification to the current user
  Future<bool> sendEmailVerification();

  /// Check if the current user's email is verified
  bool isEmailVerified();

  /// Reload the current user to get the latest verification status
  Future<bool> reloadUserAndCheckVerification();

  /// Get the current user's email address
  String? getCurrentUserEmail();

  /// Check if the current user needs email verification
  bool needsEmailVerification();

  /// Resend verification email with rate limiting check
  Future<bool> resendVerificationEmail();

  /// Listen to auth state changes for verification status
  Stream<bool> get verificationStatusStream;

  /// Sign out the current user
  Future<void> signOut();

  /// Get user creation time to determine if this is a new registration
  DateTime? getUserCreationTime();

  /// Check if this is a recent registration (within last 5 minutes)
  bool isRecentRegistration();
}

/// Implementation of EmailVerificationInterface using Firebase Auth
class FirebaseEmailVerificationService implements EmailVerificationInterface {
  final FirebaseAuth _auth;

  FirebaseEmailVerificationService({FirebaseAuth? firebaseAuth})
      : _auth = firebaseAuth ?? FirebaseAuth.instance;

  @override
  Future<bool> sendEmailVerification() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        throw Exception('No user is currently signed in');
      }

      if (user.emailVerified) {
        return true;
      }

      await user.sendEmailVerification();
      return true;
    } on FirebaseAuthException catch (e) {
      switch (e.code) {
        case 'too-many-requests':
          throw Exception('Too many verification emails sent. Please wait before requesting another.');
        case 'user-disabled':
          throw Exception('This account has been disabled.');
        case 'invalid-email':
          throw Exception('The email address is invalid.');
        default:
          throw Exception('Failed to send verification email: ${e.message}');
      }
    } catch (e) {
      throw Exception('Failed to send verification email: $e');
    }
  }

  @override
  bool isEmailVerified() {
    final user = _auth.currentUser;
    return user?.emailVerified ?? false;
  }

  @override
  Future<bool> reloadUserAndCheckVerification() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return false;
      }

      await user.reload();
      final updatedUser = _auth.currentUser;
      return updatedUser?.emailVerified ?? false;
    } catch (e) {
      return false;
    }
  }

  @override
  String? getCurrentUserEmail() {
    return _auth.currentUser?.email;
  }

  @override
  bool needsEmailVerification() {
    final user = _auth.currentUser;
    if (user == null || user.emailVerified) {
      return false;
    }

    // Check if user signed up with email/password
    final hasEmailProvider = user.providerData.any(
      (provider) => provider.providerId == 'password',
    );

    return hasEmailProvider && user.email != null;
  }

  @override
  Future<bool> resendVerificationEmail() async {
    try {
      return await sendEmailVerification();
    } on Exception catch (e) {
      if (e.toString().contains('Too many verification emails')) {
        return false; // Rate limited
      }
      rethrow; // Other errors
    }
  }

  @override
  Stream<bool> get verificationStatusStream {
    return _auth.authStateChanges().asyncMap((user) async {
      if (user == null) return false;
      
      try {
        await user.reload();
        return _auth.currentUser?.emailVerified ?? false;
      } catch (e) {
        return user.emailVerified;
      }
    });
  }

  @override
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      throw Exception('Failed to sign out: $e');
    }
  }

  @override
  DateTime? getUserCreationTime() {
    return _auth.currentUser?.metadata.creationTime;
  }

  @override
  bool isRecentRegistration() {
    final creationTime = getUserCreationTime();
    if (creationTime == null) return false;
    
    final now = DateTime.now();
    final difference = now.difference(creationTime);
    return difference.inMinutes <= 5;
  }
}
