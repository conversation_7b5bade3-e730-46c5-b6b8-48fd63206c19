import 'package:firebase_analytics/firebase_analytics.dart';
import 'logging_service.dart';

/// Centralized analytics service that integrates Firebase Analytics
/// for comprehensive user behavior tracking and event logging.
class AnalyticsService {
  static final AnalyticsService _instance = AnalyticsService._internal();
  static AnalyticsService get instance => _instance;

  AnalyticsService._internal();

  late final FirebaseAnalytics _analytics;
  bool _isInitialized = false;

  /// Initialize the analytics service
  Future<void> initialize() async {
    if (_isInitialized) return;

    _analytics = FirebaseAnalytics.instance;
    _isInitialized = true;

    LoggingService.instance.logInfo(
      'AnalyticsService',
      'Analytics service initialized',
    );
  }

  /// Set user properties for analytics segmentation
  Future<void> setUserProperty({
    required String name,
    required String value,
  }) async {
    if (!_isInitialized) return;

    try {
      await _analytics.setUserProperty(name: name, value: value);

      LoggingService.instance.logInfo(
        'AnalyticsService',
        'User property set: $name = $value',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'AnalyticsService',
        'Failed to set user property: $name',
      );
    }
  }

  /// Set user ID for analytics
  Future<void> setUserId(String userId) async {
    if (!_isInitialized) return;

    try {
      await _analytics.setUserId(id: userId);

      LoggingService.instance.logInfo(
        'AnalyticsService',
        'User ID set: $userId',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'AnalyticsService',
        'Failed to set user ID: $userId',
      );
    }
  }

  /// Log custom events with parameters
  Future<void> logEvent({
    required String name,
    Map<String, Object>? parameters,
  }) async {
    if (!_isInitialized) return;

    try {
      await _analytics.logEvent(name: name, parameters: parameters);

      LoggingService.instance.logInfo(
        'AnalyticsService',
        'Event logged: $name ${parameters ?? ''}',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'AnalyticsService',
        'Failed to log event: $name',
      );
    }
  }

  /// Log screen view events
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
  }) async {
    if (!_isInitialized) return;

    try {
      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenClass,
      );

      LoggingService.instance.logInfo(
        'AnalyticsService',
        'Screen view logged: $screenName',
      );
    } catch (e, stackTrace) {
      await LoggingService.instance.logError(
        e,
        stackTrace,
        'AnalyticsService',
        'Failed to log screen view: $screenName',
      );
    }
  }

  // === USER JOURNEY EVENTS ===

  /// Log user sign up event
  Future<void> logSignUp({String? method}) async {
    await logEvent(
      name: 'sign_up',
      parameters: method != null ? {'method': method} : null,
    );
  }

  /// Log user login event
  Future<void> logLogin({String? method}) async {
    await logEvent(
      name: 'login',
      parameters: method != null ? {'method': method} : null,
    );
  }

  /// Log onboarding completion
  Future<void> logOnboardingComplete() async {
    await logEvent(name: 'onboarding_complete');
  }

  // === CHAT EVENTS ===

  /// Log chat creation
  Future<void> logChatCreated({
    required String personaId,
    String? personaName,
  }) async {
    await logEvent(
      name: 'chat_created',
      parameters: {
        'persona_id': personaId,
        if (personaName != null) 'persona_name': personaName,
      },
    );
  }

  /// Log message sent
  Future<void> logMessageSent({
    required String chatId,
    required String messageType,
    int? messageLength,
  }) async {
    await logEvent(
      name: 'message_sent',
      parameters: {
        'chat_id': chatId,
        'message_type': messageType,
        if (messageLength != null) 'message_length': messageLength,
      },
    );
  }

  /// Log AI response received
  Future<void> logAiResponseReceived({
    required String chatId,
    required int responseTimeMs,
    int? responseLength,
  }) async {
    await logEvent(
      name: 'ai_response_received',
      parameters: {
        'chat_id': chatId,
        'response_time_ms': responseTimeMs,
        if (responseLength != null) 'response_length': responseLength,
      },
    );
  }

  // === PERSONA EVENTS ===

  /// Log persona selection
  Future<void> logPersonaSelected({
    required String personaId,
    String? personaName,
    String? selectionContext,
  }) async {
    await logEvent(
      name: 'persona_selected',
      parameters: {
        'persona_id': personaId,
        if (personaName != null) 'persona_name': personaName,
        if (selectionContext != null) 'selection_context': selectionContext,
      },
    );
  }

  // === GUIDED PATH EVENTS ===

  /// Log guided path viewed
  Future<void> logGuidedPathViewed({
    required String pathId,
    required String category,
    required String userTier,
  }) async {
    await logEvent(
      name: 'guided_path_viewed',
      parameters: {
        'path_id': pathId,
        'category': category,
        'user_tier': userTier,
      },
    );
  }

  /// Log guided path step started
  Future<void> logPathStepStarted({
    required String pathId,
    required String stepId,
    required int stepNumber,
  }) async {
    await logEvent(
      name: 'path_step_started',
      parameters: {
        'path_id': pathId,
        'step_id': stepId,
        'step_number': stepNumber,
      },
    );
  }

  /// Log guided path step completed
  Future<void> logPathStepCompleted({
    required String pathId,
    required String stepId,
    required int stepNumber,
    required int timeSpentSeconds,
  }) async {
    await logEvent(
      name: 'path_step_completed',
      parameters: {
        'path_id': pathId,
        'step_id': stepId,
        'step_number': stepNumber,
        'time_spent_seconds': timeSpentSeconds,
      },
    );
  }

  // === ADMIN EVENTS ===

  /// Log admin action performed
  Future<void> logAdminAction({
    required String action,
    Map<String, Object>? additionalParams,
  }) async {
    await logEvent(
      name: 'admin_action',
      parameters: {'action': action, ...?additionalParams},
    );
  }

  /// Check if the analytics service is initialized
  bool get isInitialized => _isInitialized;
}
