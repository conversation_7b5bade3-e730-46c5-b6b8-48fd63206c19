import 'package:flutter/foundation.dart';
import '../models/models.dart';
import '../services/system_prompt_service.dart';

/// Utility class for building comprehensive system prompts that incorporate
/// UserProfile information and context for AI coaching conversations.
class SystemPromptBuilder {
  /// Builds a system prompt for chat with persona using configurable templates
  ///
  /// This method fetches the prompt template from SystemPromptService and injects
  /// the provided parameters (systemPersona, userProfile, conversationContext, pathStep).
  ///
  /// Parameters are injected at entity level using placeholders:
  /// - {systemPersona} - Complete persona information
  /// - {userProfile} - Complete user profile information
  /// - {conversationContext} - Current conversation context
  /// - {discussionTopic} - Dynamic topic guidance based on chat context
  static Future<String> buildChatWithPersonaSystemPrompt({
    SystemPersona? systemPersona,
    UserProfile? userProfile,
    String? conversationContext,
    UserSubscription? userSubscription,
    PathStep? pathStep,
  }) async {
    try {
      // Get the prompt template from SystemPromptService
      final template = await SystemPromptService.instance
          .getChatWithPersonaPrompt(userSubscription: userSubscription);

      // Inject parameters into the template
      String prompt = template;

      // Inject system persona information
      if (systemPersona != null) {
        final personaSection = _buildPersonaSection(systemPersona);
        prompt = prompt.replaceAll('{systemPersona}', personaSection);
      } else {
        prompt = prompt.replaceAll('{systemPersona}', '');
      }

      // Inject user profile information
      if (userProfile != null) {
        final profileSection = _buildUserProfileSection(userProfile);
        prompt = prompt.replaceAll('{userProfile}', profileSection);
      } else {
        prompt = prompt.replaceAll(
          '{userProfile}',
          'No user profile information available.',
        );
      }

      // Inject conversation context
      if (conversationContext != null && conversationContext.isNotEmpty) {
        final contextSection = _buildConversationContextSection(
          conversationContext,
        );
        prompt = prompt.replaceAll('{conversationContext}', contextSection);
      } else {
        final defaultContext = _buildDefaultContextSection();
        prompt = prompt.replaceAll('{conversationContext}', defaultContext);
      }

      // Inject discussion topic guidance
      final discussionTopicSection = buildDiscussionTopicSection(pathStep);
      prompt = prompt.replaceAll('{discussionTopic}', discussionTopicSection);

      return prompt.trim();
    } catch (e) {
      // Fallback to a basic prompt if template loading fails
      final buffer = StringBuffer();

      // Basic AI role section
      if (systemPersona != null) {
        buffer.writeln(_buildPersonaSection(systemPersona));
      } else {
        buffer.writeln('# AI Coach Role & Identity');
        buffer.writeln();
        buffer.writeln(
          'You are an AI coaching assistant designed to provide personalized guidance, support, and motivation.',
        );
      }

      buffer.writeln();
      buffer.writeln(_buildDefaultContextSection());

      if (userProfile != null) {
        buffer.writeln();
        buffer.writeln(_buildUserProfileSection(userProfile));
      }

      final fallbackPrompt = buffer.toString().trim();
      // Debug: Log the generated fallback system prompt
      debugPrint('DEBUG: SystemPromptBuilder fallback prompt: $fallbackPrompt');

      return fallbackPrompt;
    }
  }

  /// Builds persona section for template injection
  static String _buildPersonaSection(SystemPersona systemPersona) {
    final buffer = StringBuffer();

    buffer.writeln(
      'You are embodying the coaching persona: **${systemPersona.name}**',
    );

    if (systemPersona.description != null &&
        systemPersona.description!.isNotEmpty) {
      buffer.writeln();
      buffer.writeln('**Persona Description**: ${systemPersona.description}');
      buffer.writeln();
      buffer.writeln(
        'Embody this persona\'s unique coaching style and approach while maintaining these core characteristics:',
      );
    }

    buffer.writeln();

    return buffer.toString().trimRight();
  }

  /// Builds conversation context section for template injection
  static String _buildConversationContextSection(String context) {
    final now = DateTime.now();
    final timeZone = now.timeZoneName;
    final formattedTime = now.toIso8601String();
    final dayOfWeek = _getDayOfWeek(now.weekday);

    return '''
- **Current Time**: $formattedTime ($timeZone)
- **Day**: $dayOfWeek
- **Session Type**: AI Coaching Conversation
- **Context**: $context''';
  }

  /// Builds default context section when no specific context is provided
  static String _buildDefaultContextSection() {
    final now = DateTime.now();
    final timeZone = now.timeZoneName;
    final formattedTime = now.toIso8601String();
    final dayOfWeek = _getDayOfWeek(now.weekday);

    return '''
- **Current Time**: $formattedTime ($timeZone)
- **Day**: $dayOfWeek
- **Session Type**: AI Coaching Conversation''';
  }

  static String _buildUserProfileSection(UserProfile userProfile) {
    final buffer = StringBuffer();

    // Basic Information
    if (userProfile.name != null ||
        userProfile.age != null ||
        userProfile.gender != null) {
      buffer.writeln('## Basic Information');
      if (userProfile.name != null) {
        buffer.writeln('- **Name**: ${userProfile.name}');
      }
      if (userProfile.age != null) {
        buffer.writeln('- **Age**: ${userProfile.age}');
      }
      if (userProfile.gender != null) {
        buffer.writeln('- **Gender**: ${userProfile.gender}');
      }
      if (userProfile.familyStatus != null) {
        buffer.writeln('- **Family Status**: ${userProfile.familyStatus}');
      }
      if (userProfile.location != null) {
        final location = userProfile.location!;
        final locationStr = location.town != null
            ? '${location.town}, ${location.country}'
            : location.country;
        buffer.writeln('- **Location**: $locationStr');
      }
      buffer.writeln();
    }

    // Family & Relationships
    if (userProfile.family != null && userProfile.family!.isNotEmpty) {
      buffer.writeln('## Family & Relationships');
      for (final relation in userProfile.family!) {
        buffer.write(
          '- **${relation.name}** (${relation.relation}, age ${relation.age})',
        );
        if (relation.otherInfo != null && relation.otherInfo!.isNotEmpty) {
          buffer.write(' - ${relation.otherInfo!.join(', ')}');
        }
        buffer.writeln();
      }
      buffer.writeln();
    }

    // Goals
    if (userProfile.goals != null && userProfile.goals!.isNotEmpty) {
      buffer.writeln('## Current Goals');
      for (final goal in userProfile.goals!) {
        buffer.writeln('- **${goal.description}** (Status: ${goal.status})');
      }
      buffer.writeln();
    }

    // Preferences & Interests
    if (userProfile.likes != null && userProfile.likes!.isNotEmpty) {
      buffer.writeln('## Interests & Likes');
      buffer.writeln('- ${userProfile.likes!.join(', ')}');
      buffer.writeln();
    }

    if (userProfile.dislikes != null && userProfile.dislikes!.isNotEmpty) {
      buffer.writeln('## Dislikes');
      buffer.writeln('- ${userProfile.dislikes!.join(', ')}');
      buffer.writeln();
    }

    // Personality Traits
    if (userProfile.personalityTraits != null &&
        userProfile.personalityTraits!.isNotEmpty) {
      buffer.writeln('## Personality Traits');
      buffer.writeln('- ${userProfile.personalityTraits!.join(', ')}');
      buffer.writeln();
    }

    // Key Facts
    if (userProfile.facts != null && userProfile.facts!.isNotEmpty) {
      buffer.writeln('## Important Facts');
      for (final fact in userProfile.facts!) {
        buffer.writeln('- **${fact.key}**: ${_formatFactValue(fact.value)}');
      }
      buffer.writeln();
    }

    // Preferences
    if (userProfile.preferences != null &&
        userProfile.preferences!.isNotEmpty) {
      buffer.writeln('## Communication Preferences');
      userProfile.preferences!.forEach((key, value) {
        buffer.writeln('- **$key**: $value');
      });
      buffer.writeln();
    }

    return buffer.toString().trimRight();
  }

  /// Safely converts a fact value (dynamic type) to a String for display
  /// Handles null, String, int, bool, and other types appropriately
  static String _formatFactValue(dynamic value) {
    if (value == null) {
      return 'Not set';
    }

    if (value is String) {
      return value.isEmpty ? 'Empty' : value;
    }

    if (value is int || value is double) {
      return value.toString();
    }

    if (value is bool) {
      return value ? 'Yes' : 'No';
    }

    // For any other type, convert to string
    return value.toString();
  }

  static String _getDayOfWeek(int weekday) {
    const days = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday',
    ];
    return days[weekday - 1];
  }

  /// Builds the discussion topic section based on chat context
  ///
  /// For PathStep-initiated chats: Creates topic guidance from the step's
  /// title, description, reflection prompts, and completion criteria.
  /// For general chats: Provides default check-in topic guidance.
  static String buildDiscussionTopicSection(PathStep? pathStep) {
    final buffer = StringBuffer();

    if (pathStep != null) {
      // PathStep-initiated chat: Create topic guidance from step content
      buffer.writeln('## Session Focus: ${pathStep.title}');
      buffer.writeln();
      buffer.writeln('**Context:** ${pathStep.description}');
      buffer.writeln();

      if (pathStep.reflectionPrompts?.isNotEmpty == true) {
        buffer.writeln('**Key Reflection Areas:**');
        for (final prompt in pathStep.reflectionPrompts!) {
          buffer.writeln('- $prompt');
        }
        buffer.writeln();
      }

      buffer.writeln('**Session Goal:** ${pathStep.completionCriteria}');
      buffer.writeln();
      buffer.writeln('**Coaching Instructions:**');
      buffer.writeln(
        '- Focus the conversation on helping the user work through this specific step',
      );
      buffer.writeln(
        '- Guide them through the reflection prompts naturally during conversation',
      );
      buffer.writeln(
        '- Help them understand and apply the concepts to their personal situation',
      );
      buffer.writeln(
        '- When the user has adequately covered the reflection areas and understands the concepts, acknowledge their progress and thank them for engaging with this step',
      );
      buffer.writeln('- Encourage them to continue their growth journey');
    } else {
      // General chat: Default check-in topic guidance
      buffer.writeln(
        '## Session Focus: Personal Check-In & Growth Exploration',
      );
      buffer.writeln();
      buffer.writeln(
        '**Context:** This is an open coaching conversation where the user can explore any aspect of their personal growth journey.',
      );
      buffer.writeln();
      buffer.writeln('**Coaching Instructions:**');
      buffer.writeln(
        '- Start with open-ended questions to understand what\'s on their mind today',
      );
      buffer.writeln(
        '- Ask questions like "What\'s on your mind today?" or "How can I help you today?" or "What would you like to explore in our conversation?"',
      );
      buffer.writeln(
        '- Listen actively and follow their lead while providing supportive guidance',
      );
      buffer.writeln(
        '- Help them identify areas for growth, reflection, or action',
      );
      buffer.writeln(
        '- Encourage deeper self-reflection through thoughtful questions',
      );
      buffer.writeln(
        '- Provide practical insights and actionable next steps when appropriate',
      );
    }

    return buffer.toString().trim();
  }
}
