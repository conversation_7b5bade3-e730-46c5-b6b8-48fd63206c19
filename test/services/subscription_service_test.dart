import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart';
import 'package:upshift/services/subscription_service.dart';

void main() {
  group('SubscriptionService', () {
    test('should create singleton instance', () {
      final instance1 = SubscriptionService.instance;
      final instance2 = SubscriptionService.instance;
      
      expect(instance1, same(instance2));
    });

    test('should start with free subscription', () {
      final service = SubscriptionService.instance;
      final subscription = service.currentSubscription;
      
      expect(subscription.isActive, false);
      expect(subscription.entitlements, isEmpty);
      expect(subscription.hasPremiumAccess, false);
      expect(subscription.userTier, 'free');
    });

    test('should not be initialized by default', () {
      final service = SubscriptionService.instance;
      expect(service.isInitialized, false);
    });
  });

  group('UserSubscription', () {
    test('should create free subscription correctly', () {
      final subscription = UserSubscription.free();
      
      expect(subscription.isActive, false);
      expect(subscription.entitlements, isEmpty);
      expect(subscription.hasPremiumAccess, false);
      expect(subscription.userTier, 'free');
      expect(subscription.willRenew, false);
    });

    test('should create premium subscription correctly', () {
      final subscription = UserSubscription(
        isActive: true,
        entitlements: [SubscriptionEntitlement.premium],
        subscriptionType: 'monthly',
        willRenew: true,
        lastChecked: DateTime.now(),
      );
      
      expect(subscription.isActive, true);
      expect(subscription.entitlements, contains(SubscriptionEntitlement.premium));
      expect(subscription.hasPremiumAccess, true);
      expect(subscription.userTier, 'paid');
      expect(subscription.willRenew, true);
      expect(subscription.hasEntitlement(SubscriptionEntitlement.premium), true);
    });

    test('should handle copyWith correctly', () {
      final original = UserSubscription.free();
      final updated = original.copyWith(
        isActive: true,
        entitlements: [SubscriptionEntitlement.premium],
      );
      
      expect(original.isActive, false);
      expect(updated.isActive, true);
      expect(updated.entitlements, contains(SubscriptionEntitlement.premium));
      expect(updated.hasPremiumAccess, true);
    });
  });

  group('SubscriptionEntitlement', () {
    test('should have correct identifier', () {
      expect(SubscriptionEntitlement.premium.identifier, 'premium');
    });

    test('should have correct display name', () {
      expect(SubscriptionEntitlement.premium.displayName, 'Premium');
    });

    test('should be premium', () {
      expect(SubscriptionEntitlement.premium.isPremium, true);
    });
  });
}
