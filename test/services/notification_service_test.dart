import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:upshift/services/notification_service.dart';
import 'notification_service_test.mocks.dart';

// Generate mocks for Firebase services
@GenerateMocks([FirebaseMessaging, FirebaseAuth, User, NotificationSettings])
void main() {
  group('NotificationService', () {
    late NotificationService notificationService;
    late MockFirebaseMessaging mockFirebaseMessaging;
    late MockFirebaseAuth mockFirebaseAuth;
    late MockUser mockUser;
    late MockNotificationSettings mockNotificationSettings;

    setUp(() {
      // Get singleton instance for each test
      notificationService = NotificationService.instance;

      mockFirebaseMessaging = MockFirebaseMessaging();
      mockFirebaseAuth = MockFirebaseAuth();
      mockUser = MockUser();
      mockNotificationSettings = MockNotificationSettings();
    });

    group('Initialization', () {
      test('should initialize successfully', () async {
        // Arrange
        when(
          mockFirebaseMessaging.requestPermission(
            alert: anyNamed('alert'),
            announcement: anyNamed('announcement'),
            badge: anyNamed('badge'),
            carPlay: anyNamed('carPlay'),
            criticalAlert: anyNamed('criticalAlert'),
            provisional: anyNamed('provisional'),
            sound: anyNamed('sound'),
          ),
        ).thenAnswer((_) async => mockNotificationSettings);

        when(
          mockNotificationSettings.authorizationStatus,
        ).thenReturn(AuthorizationStatus.authorized);

        when(
          mockFirebaseMessaging.getToken(),
        ).thenAnswer((_) async => 'test_token');

        // Mock the onTokenRefresh stream
        when(
          mockFirebaseMessaging.onTokenRefresh,
        ).thenAnswer((_) => Stream.value('new_token'));

        // Act & Assert
        expect(() => notificationService.initialize(), returnsNormally);
      });

      test('should handle initialization failure gracefully', () async {
        // Arrange
        when(
          mockFirebaseMessaging.requestPermission(
            alert: anyNamed('alert'),
            announcement: anyNamed('announcement'),
            badge: anyNamed('badge'),
            carPlay: anyNamed('carPlay'),
            criticalAlert: anyNamed('criticalAlert'),
            provisional: anyNamed('provisional'),
            sound: anyNamed('sound'),
          ),
        ).thenThrow(Exception('Permission request failed'));

        // Act & Assert
        expect(
          () => notificationService.initialize(),
          throwsA(isA<Exception>()),
        );
      });

      test('should not initialize twice', () async {
        // Arrange
        when(
          mockFirebaseMessaging.requestPermission(
            alert: anyNamed('alert'),
            announcement: anyNamed('announcement'),
            badge: anyNamed('badge'),
            carPlay: anyNamed('carPlay'),
            criticalAlert: anyNamed('criticalAlert'),
            provisional: anyNamed('provisional'),
            sound: anyNamed('sound'),
          ),
        ).thenAnswer((_) async => mockNotificationSettings);

        when(
          mockNotificationSettings.authorizationStatus,
        ).thenReturn(AuthorizationStatus.authorized);

        when(
          mockFirebaseMessaging.getToken(),
        ).thenAnswer((_) async => 'test_token');

        when(
          mockFirebaseMessaging.onTokenRefresh,
        ).thenAnswer((_) => Stream.value('new_token'));

        // Act
        await notificationService.initialize();
        expect(notificationService.isInitialized, isTrue);

        // Second initialization should not throw
        await notificationService.initialize();
        expect(notificationService.isInitialized, isTrue);
      });
    });

    group('Token Management', () {
      test('should get and store FCM token', () async {
        // Arrange
        const testToken = 'test_fcm_token';
        when(
          mockFirebaseMessaging.getToken(),
        ).thenAnswer((_) async => testToken);

        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn('test_user_id');

        // Act
        await notificationService.initialize();

        // Assert
        expect(notificationService.currentToken, equals(testToken));
      });

      test('should handle null token gracefully', () async {
        // Arrange
        when(mockFirebaseMessaging.getToken()).thenAnswer((_) async => null);

        // Act
        await notificationService.initialize();

        // Assert
        expect(notificationService.currentToken, isNull);
      });
    });

    group('Topic Subscription', () {
      test('should subscribe to topic successfully', () async {
        // Arrange
        const topic = 'test_topic';
        when(
          mockFirebaseMessaging.subscribeToTopic(topic),
        ).thenAnswer((_) async {});

        // Act & Assert
        expect(
          () => notificationService.subscribeToTopic(topic),
          returnsNormally,
        );
      });

      test('should unsubscribe from topic successfully', () async {
        // Arrange
        const topic = 'test_topic';
        when(
          mockFirebaseMessaging.unsubscribeFromTopic(topic),
        ).thenAnswer((_) async {});

        // Act & Assert
        expect(
          () => notificationService.unsubscribeFromTopic(topic),
          returnsNormally,
        );
      });

      test('should handle subscription failure', () async {
        // Arrange
        const topic = 'test_topic';
        when(
          mockFirebaseMessaging.subscribeToTopic(topic),
        ).thenThrow(Exception('Subscription failed'));

        // Act & Assert
        expect(
          () => notificationService.subscribeToTopic(topic),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('Permission Status', () {
      test('should get permission status', () async {
        // Arrange
        when(
          mockFirebaseMessaging.getNotificationSettings(),
        ).thenAnswer((_) async => mockNotificationSettings);
        when(
          mockNotificationSettings.authorizationStatus,
        ).thenReturn(AuthorizationStatus.authorized);

        // Act
        final status = await notificationService.getPermissionStatus();

        // Assert
        expect(status, equals(AuthorizationStatus.authorized));
      });

      test('should handle permission status error', () async {
        // Arrange
        when(
          mockFirebaseMessaging.getNotificationSettings(),
        ).thenThrow(Exception('Failed to get settings'));

        // Act
        final status = await notificationService.getPermissionStatus();

        // Assert
        expect(status, equals(AuthorizationStatus.notDetermined));
      });
    });

    group('Cleanup', () {
      test('should dispose resources properly', () {
        // Act
        notificationService.dispose();

        // Assert
        expect(notificationService.isInitialized, isFalse);
      });
    });
  });
}
