import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart';

/// Test helper function that mimics the _formatFactValue method from privacy_settings.dart
String formatFactValue(dynamic value) {
  if (value == null) {
    return 'Not set';
  }
  
  if (value is String) {
    return value.isEmpty ? 'Empty' : value;
  }
  
  if (value is int || value is double) {
    return value.toString();
  }
  
  if (value is bool) {
    return value ? 'Yes' : 'No';
  }
  
  // For any other type, convert to string
  return value.toString();
}

void main() {
  group('Privacy Settings Fact Formatting Tests', () {
    test('formatFactValue handles all supported types correctly', () {
      // Test string values
      expect(formatFactValue('Hello World'), equals('Hello World'));
      expect(formatFactValue(''), equals('Empty'));
      
      // Test numeric values
      expect(formatFactValue(42), equals('42'));
      expect(formatFactValue(3.14), equals('3.14'));
      expect(formatFactValue(0), equals('0'));
      
      // Test boolean values
      expect(formatFactValue(true), equals('Yes'));
      expect(formatFactValue(false), equals('No'));
      
      // Test null value
      expect(formatFactValue(null), equals('Not set'));
      
      // Test complex types (should fall back to toString)
      expect(formatFactValue([1, 2, 3]), equals('[1, 2, 3]'));
      expect(formatFactValue({'key': 'value'}), equals('{key: value}'));
    });

    test('Facts with various value types can be displayed safely', () {
      final facts = [
        Fact(key: 'Name', value: 'John Doe'),
        Fact(key: 'Age', value: 30),
        Fact(key: 'Height', value: 5.9),
        Fact(key: 'Is Married', value: true),
        Fact(key: 'Has Children', value: false),
        Fact(key: 'Nickname', value: null),
        Fact(key: 'Empty Field', value: ''),
        Fact(key: 'Hobbies', value: ['reading', 'swimming']),
      ];

      // Verify that all fact values can be safely converted to strings for display
      for (final fact in facts) {
        final displayValue = formatFactValue(fact.value);
        expect(displayValue, isA<String>());
        expect(displayValue.isNotEmpty, isTrue);
      }

      // Verify specific expected outputs
      expect(formatFactValue(facts[0].value), equals('John Doe'));
      expect(formatFactValue(facts[1].value), equals('30'));
      expect(formatFactValue(facts[2].value), equals('5.9'));
      expect(formatFactValue(facts[3].value), equals('Yes'));
      expect(formatFactValue(facts[4].value), equals('No'));
      expect(formatFactValue(facts[5].value), equals('Not set'));
      expect(formatFactValue(facts[6].value), equals('Empty'));
      expect(formatFactValue(facts[7].value), equals('[reading, swimming]'));
    });

    test('UserProfile with mixed fact types normalizes correctly for storage', () {
      final userProfile = UserProfile(
        userId: 'test-user',
        facts: [
          Fact(key: 'occupation', value: 'Software Engineer'),
          Fact(key: 'years_experience', value: 5),
          Fact(key: 'salary', value: 75000.50),
          Fact(key: 'remote_work', value: true),
          Fact(key: 'has_degree', value: false),
          Fact(key: 'certifications', value: null),
        ],
        interactionHistory: InteractionHistory(
          lastUpdated: DateTime.now(),
          sources: null,
        ),
      );

      // Normalize for storage
      final normalizedProfile = userProfile.withNormalizedFactValues();

      // Verify all fact values are now strings
      for (final fact in normalizedProfile.facts!) {
        expect(fact.value, isA<String>());
      }

      // Verify specific conversions
      expect(normalizedProfile.facts![0].value, equals('Software Engineer'));
      expect(normalizedProfile.facts![1].value, equals('5'));
      expect(normalizedProfile.facts![2].value, equals('75000.5'));
      expect(normalizedProfile.facts![3].value, equals('true'));
      expect(normalizedProfile.facts![4].value, equals('false'));
      expect(normalizedProfile.facts![5].value, equals(''));
    });

    test('Fact values remain displayable after normalization', () {
      final originalFacts = [
        Fact(key: 'net_worth', value: 2000000),
        Fact(key: 'annual_income', value: 700000),
        Fact(key: 'is_homeowner', value: true),
        Fact(key: 'spouse_name', value: 'Jane Doe'),
      ];

      final userProfile = UserProfile(
        userId: 'test-user',
        facts: originalFacts,
        interactionHistory: InteractionHistory(
          lastUpdated: DateTime.now(),
          sources: null,
        ),
      );

      final normalizedProfile = userProfile.withNormalizedFactValues();

      // Verify that normalized values can still be displayed properly
      for (int i = 0; i < normalizedProfile.facts!.length; i++) {
        final normalizedFact = normalizedProfile.facts![i];
        final displayValue = formatFactValue(normalizedFact.value);
        
        expect(displayValue, isA<String>());
        expect(displayValue.isNotEmpty, isTrue);
      }

      // Verify specific display values
      expect(formatFactValue(normalizedProfile.facts![0].value), equals('2000000'));
      expect(formatFactValue(normalizedProfile.facts![1].value), equals('700000'));
      expect(formatFactValue(normalizedProfile.facts![2].value), equals('true')); // Will display as 'true' since it's now a string
      expect(formatFactValue(normalizedProfile.facts![3].value), equals('Jane Doe'));
    });
  });
}
