import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:firebase_auth/firebase_auth.dart' as auth;
import 'package:upshift/pages/notifications_page.dart';
import 'package:upshift/theme/theme.dart';

import 'notifications_page_test.mocks.dart';

// Generate mocks for dependencies
@GenerateMocks([auth.FirebaseAuth, auth.User])
void main() {
  group('NotificationsPage', () {
    late MockFirebaseAuth mockFirebaseAuth;
    late MockUser mockUser;

    setUp(() {
      mockFirebaseAuth = MockFirebaseAuth();
      mockUser = MockUser();
    });

    Widget createTestWidget() {
      return MaterialApp(
        theme: AppTheme.lightTheme,
        home: const NotificationsPage(),
      );
    }

    group('Widget Structure', () {
      testWidgets('should display app bar with correct title', (tester) async {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn('test_user_id');

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pump(); // Allow for async operations

        // Assert
        expect(find.text('Notification Settings'), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
      });

      testWidgets('should show loading indicator initially', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });

      testWidgets('should display error message when loading fails', (
        tester,
      ) async {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        expect(find.text('Retry'), findsOneWidget);
      });
    });

    group('Notification Preferences', () {
      testWidgets('should display master toggle switches', (tester) async {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn('test_user_id');

        // Mock successful data loading would happen here

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.text('Notification Preferences'), findsOneWidget);
        expect(find.text('Push Notifications'), findsOneWidget);
        expect(find.text('Email Notifications'), findsOneWidget);
        expect(find.byType(Switch), findsAtLeastNWidgets(2));
      });

      testWidgets(
        'should display topic preferences when push notifications enabled',
        (tester) async {
          // Arrange
          when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
          when(mockUser.uid).thenReturn('test_user_id');

          // Mock user data would be set up here

          // Act
          await tester.pumpWidget(createTestWidget());
          await tester.pumpAndSettle();

          // Assert
          expect(find.text('Notification Types'), findsOneWidget);
          expect(find.text('Chat Messages'), findsOneWidget);
          expect(find.text('Guided Path Updates'), findsOneWidget);
          expect(find.text('System Announcements'), findsOneWidget);
          expect(find.text('Weekly Insights'), findsOneWidget);
        },
      );

      testWidgets('should toggle push notifications preference', (
        tester,
      ) async {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn('test_user_id');

        // Mock user data would be set up here

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Find the push notifications switch
        final pushNotificationSwitch = find.byKey(
          const ValueKey('push_notifications_switch'),
        );

        if (pushNotificationSwitch.evaluate().isNotEmpty) {
          await tester.tap(pushNotificationSwitch);
          await tester.pump();
        }

        // Assert - The save button should appear when changes are made
        // This test verifies the UI responds to user interaction
        expect(find.byType(Switch), findsAtLeastNWidgets(2));
      });
    });

    group('Save Functionality', () {
      testWidgets('should show save button when changes are made', (
        tester,
      ) async {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn('test_user_id');

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // The save button should appear in the app bar when changes are detected
        // This is tested through the UI state management
        expect(find.byType(AppBar), findsOneWidget);
      });

      testWidgets('should handle save operation', (tester) async {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn('test_user_id');

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Look for save button if it exists
        final saveButton = find.text('Save');
        if (saveButton.evaluate().isNotEmpty) {
          await tester.tap(saveButton);
          await tester.pump();
        }

        // Assert - The UI should handle the save operation
        expect(find.byType(NotificationsPage), findsOneWidget);
      });
    });

    group('Error Handling', () {
      testWidgets('should display error message with retry button', (
        tester,
      ) async {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
        expect(find.text('Retry'), findsOneWidget);
        expect(find.byType(ElevatedButton), findsOneWidget);
      });

      testWidgets('should retry loading when retry button is tapped', (
        tester,
      ) async {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Tap retry button
        await tester.tap(find.text('Retry'));
        await tester.pump();

        // Assert - Should attempt to reload
        expect(find.byType(NotificationsPage), findsOneWidget);
      });
    });

    group('Accessibility', () {
      testWidgets('should have proper semantics for screen readers', (
        tester,
      ) async {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn('test_user_id');

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert - Check for semantic labels
        expect(find.text('Push Notifications'), findsOneWidget);
        expect(find.text('Email Notifications'), findsOneWidget);
        expect(find.byType(Switch), findsAtLeastNWidgets(2));
      });

      testWidgets('should support keyboard navigation', (tester) async {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn('test_user_id');

        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Assert - Switches should be focusable
        expect(find.byType(Switch), findsAtLeastNWidgets(2));
      });
    });
  });
}
