import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/pages/email_verification_page.dart';
import 'package:upshift/theme/theme.dart';

void main() {
  group('EmailVerificationPage', () {
    Widget createTestWidget() {
      return MaterialApp(
        theme: AppTheme.lightTheme,
        home: const EmailVerificationPage(),
      );
    }

    testWidgets('displays email verification UI elements', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Verify Your Email'), findsOneWidget);
      expect(find.text('We\'ve sent a verification link to:'), findsOneWidget);
      expect(find.text('I\'ve Verified My Email'), findsOneWidget);
      expect(find.text('Resend Verification Email'), findsOneWidget);
      expect(find.text('Sign in with a different account'), findsOneWidget);
    });

    testWidgets('displays email verification icon', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byIcon(AppIcons.emailUnverified), findsOneWidget);
    });

    testWidgets('shows instructions text', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(
        find.text(
          'Please check your email and click the verification link to continue. You may need to check your spam folder.',
        ),
        findsOneWidget,
      );
    });

    testWidgets('check verification button is enabled by default', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final checkButton = find.widgetWithText(ElevatedButton, 'I\'ve Verified My Email');
      expect(checkButton, findsOneWidget);
      
      final button = tester.widget<ElevatedButton>(checkButton);
      expect(button.onPressed, isNotNull);
    });

    testWidgets('resend button is enabled by default', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final resendButton = find.widgetWithText(OutlinedButton, 'Resend Verification Email');
      expect(resendButton, findsOneWidget);
      
      final button = tester.widget<OutlinedButton>(resendButton);
      expect(button.onPressed, isNotNull);
    });

    testWidgets('sign out button is always enabled', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final signOutButton = find.widgetWithText(TextButton, 'Sign in with a different account');
      expect(signOutButton, findsOneWidget);
      
      final button = tester.widget<TextButton>(signOutButton);
      expect(button.onPressed, isNotNull);
    });

    testWidgets('tapping check verification button shows loading state', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());

      // Act
      await tester.tap(find.widgetWithText(ElevatedButton, 'I\'ve Verified My Email'));
      await tester.pump(); // Trigger rebuild

      // Assert
      expect(find.text('Checking...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsAtLeastNWidgets(1));
    });

    testWidgets('tapping resend button shows loading state', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());

      // Act
      await tester.tap(find.widgetWithText(OutlinedButton, 'Resend Verification Email'));
      await tester.pump(); // Trigger rebuild

      // Assert
      expect(find.text('Sending...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsAtLeastNWidgets(1));
    });

    group('Error and Success Messages', () {
      testWidgets('can display error messages', (WidgetTester tester) async {
        // This test would require mocking the EmailVerificationService
        // to simulate error conditions. For now, we just verify the UI structure.
        await tester.pumpWidget(createTestWidget());
        
        // The error message container should not be visible initially
        expect(find.byIcon(AppIcons.error), findsNothing);
      });

      testWidgets('can display success messages', (WidgetTester tester) async {
        // This test would require mocking the EmailVerificationService
        // to simulate success conditions. For now, we just verify the UI structure.
        await tester.pumpWidget(createTestWidget());
        
        // The success message container should not be visible initially
        expect(find.byIcon(AppIcons.success), findsNothing);
      });
    });

    group('Accessibility', () {
      testWidgets('has proper semantic labels', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());

        // Assert - Check that important elements are accessible
        expect(find.text('Verify Your Email'), findsOneWidget);
        expect(find.text('I\'ve Verified My Email'), findsOneWidget);
        expect(find.text('Resend Verification Email'), findsOneWidget);
      });

      testWidgets('buttons have proper touch targets', (WidgetTester tester) async {
        // Arrange & Act
        await tester.pumpWidget(createTestWidget());

        // Assert - Check button sizes meet accessibility guidelines
        final checkButton = find.widgetWithText(ElevatedButton, 'I\'ve Verified My Email');
        final resendButton = find.widgetWithText(OutlinedButton, 'Resend Verification Email');
        
        expect(checkButton, findsOneWidget);
        expect(resendButton, findsOneWidget);
        
        // Buttons should be large enough for touch interaction
        final checkButtonSize = tester.getSize(checkButton);
        final resendButtonSize = tester.getSize(resendButton);
        
        expect(checkButtonSize.height, greaterThanOrEqualTo(44.0)); // iOS minimum
        expect(resendButtonSize.height, greaterThanOrEqualTo(44.0)); // iOS minimum
      });
    });
  });
}
