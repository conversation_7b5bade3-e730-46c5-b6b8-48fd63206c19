import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/pages/privacy_settings.dart';

void main() {
  group('PrivacySettingsPage', () {
    testWidgets('should render without crashing', (WidgetTester tester) async {
      // Build the widget
      await tester.pumpWidget(MaterialApp(home: const PrivacySettingsPage()));

      // Verify the page renders (it will show loading initially)
      expect(find.byType(PrivacySettingsPage), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('Privacy & Data Settings'), findsOneWidget);
    });

    testWidgets('should show error state when Firebase is not initialized', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(MaterialApp(home: const PrivacySettingsPage()));

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Should show error state since Firebase is not initialized in tests
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('Error loading data'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('should have proper app bar structure', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(MaterialApp(home: const PrivacySettingsPage()));

      // Verify app bar elements
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('Privacy & Data Settings'), findsOneWidget);
    });

    testWidgets('should be accessible', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(home: const PrivacySettingsPage()));

      // Check for semantic structure
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);

      // The page should have proper semantic labels
      final scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.appBar, isNotNull);
    });
  });
}
