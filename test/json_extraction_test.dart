import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart';
import 'dart:convert';

void main() {
  group('JSON Extraction and DateTime Conversion Tests', () {
    test('should extract <PERSON><PERSON><PERSON> from markdown and handle DateTime conversion', () {
      // Simulate the exact response format from Gemini AI (from example file)
      const geminiResponse = '''```json
{
    "userId": "Gnv2pzOtkfUwyMLiaLAhJylGGU33",
    "name": "User",
    "age": null,
    "gender": null,
    "familyStatus": null,
    "family": null,
    "location": null,
    "facts": [
        {
            "key": "current_net_worth",
            "value": 2000000
        },
        {
            "key": "current_annual_income",
            "value": 700000
        }
    ],
    "likes": null,
    "dislikes": null,
    "preferences": null,
    "goals": [
        {
            "id": "goal_finance_net_worth_1",
            "description": "Achieve \$10M net worth by 2029",
            "status": "in_progress",
            "createdAt": "2025-07-09T20:00:44.235Z",
            "updatedAt": "2025-07-09T20:00:44.235Z"
        }
    ],
    "personalityTraits": [
        "ambitious",
        "goal-oriented"
    ],
    "interactionHistory": {
        "lastUpdated": "2025-07-09T20:00:44.235Z",
        "sources": [
            {
                "sessionId": "initialization",
                "timestamp": "2025-07-09T20:00:37.767Z"
            }
        ]
    }
}
```''';

      // Step 1: Extract JSON from markdown (simulating _extractJsonFromResponse)
      final trimmedResponse = geminiResponse.trim();
      String extractedJson = trimmedResponse;

      if (trimmedResponse.startsWith('```json') &&
          trimmedResponse.endsWith('```')) {
        final startIndex = trimmedResponse.indexOf('```json') + 7;
        final endIndex = trimmedResponse.lastIndexOf('```');
        if (startIndex < endIndex) {
          extractedJson = trimmedResponse
              .substring(startIndex, endIndex)
              .trim();
        }
      }

      // Step 2: Parse the extracted JSON
      final parsedJson = jsonDecode(extractedJson) as Map<String, dynamic>;

      // Step 3: Create UserProfile from parsed JSON (TimestampDateTimeConverter will handle ISO 8601 strings)
      final userProfile = UserProfile.fromJson(parsedJson);

      // Verify the UserProfile was created correctly
      expect(userProfile.userId, equals('Gnv2pzOtkfUwyMLiaLAhJylGGU33'));
      expect(userProfile.name, equals('User'));
      expect(userProfile.age, isNull);
      expect(userProfile.facts?.length, equals(2));
      expect(userProfile.facts?[0].key, equals('current_net_worth'));
      expect(userProfile.facts?[0].value, equals(2000000));

      // Verify DateTime conversion worked
      expect(userProfile.interactionHistory.lastUpdated, isA<DateTime>());
      expect(
        userProfile.interactionHistory.lastUpdated.toUtc().toIso8601String(),
        equals('2025-07-09T20:00:44.235Z'),
      );

      expect(userProfile.interactionHistory.sources?.length, equals(1));
      expect(
        userProfile.interactionHistory.sources?[0].timestamp,
        isA<DateTime>(),
      );
      expect(
        userProfile.interactionHistory.sources?[0].timestamp
            .toUtc()
            .toIso8601String(),
        equals('2025-07-09T20:00:37.767Z'),
      );

      expect(userProfile.goals?.length, equals(1));
      expect(userProfile.goals?[0].createdAt, isA<DateTime>());
      expect(
        userProfile.goals?[0].createdAt.toUtc().toIso8601String(),
        equals('2025-07-09T20:00:44.235Z'),
      );
      expect(userProfile.goals?[0].updatedAt, isA<DateTime>());
      expect(
        userProfile.goals?[0].updatedAt?.toUtc().toIso8601String(),
        equals('2025-07-09T20:00:44.235Z'),
      );

      expect(userProfile.personalityTraits?.length, equals(2));
      expect(userProfile.personalityTraits?[0], equals('ambitious'));
      expect(userProfile.personalityTraits?[1], equals('goal-oriented'));
    });

    test('should handle plain JSON without markdown formatting', () {
      const plainJsonResponse = '''
{
    "userId": "test-user-456",
    "name": "Plain User",
    "interactionHistory": {
        "lastUpdated": "2025-07-09T15:30:00.000Z"
    }
}''';

      // Extract JSON (should remain unchanged for plain JSON)
      final trimmedResponse = plainJsonResponse.trim();
      String extractedJson = trimmedResponse;

      if (trimmedResponse.startsWith('```json') &&
          trimmedResponse.endsWith('```')) {
        final startIndex = trimmedResponse.indexOf('```json') + 7;
        final endIndex = trimmedResponse.lastIndexOf('```');
        if (startIndex < endIndex) {
          extractedJson = trimmedResponse
              .substring(startIndex, endIndex)
              .trim();
        }
      }

      // Parse and create UserProfile (TimestampDateTimeConverter handles ISO 8601 strings)
      final parsedJson = jsonDecode(extractedJson) as Map<String, dynamic>;
      final userProfile = UserProfile.fromJson(parsedJson);

      // Verify
      expect(userProfile.userId, equals('test-user-456'));
      expect(userProfile.name, equals('Plain User'));
      expect(userProfile.interactionHistory.lastUpdated, isA<DateTime>());
      expect(
        userProfile.interactionHistory.lastUpdated.toUtc().toIso8601String(),
        equals('2025-07-09T15:30:00.000Z'),
      );
    });
  });
}
