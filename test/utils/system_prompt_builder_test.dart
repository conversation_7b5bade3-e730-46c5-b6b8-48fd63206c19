import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/utils/system_prompt_builder.dart';
import 'package:upshift/models/models.dart';

void main() {
  group('SystemPromptBuilder', () {
    test('builds basic system prompt without user profile', () async {
      final prompt = await SystemPromptBuilder.buildChatWithPersonaSystemPrompt(
        conversationContext: 'Test coaching session',
      );

      expect(prompt, contains('AI Coach Role & Identity'));
      // Note: In test environment, SystemPromptService is not initialized,
      // so it falls back to basic prompt without Current Context section
      expect(prompt, contains('coaching assistant'));
    });

    test('builds comprehensive system prompt with user profile', () async {
      final userProfile = UserProfile(
        userId: 'test-user-123',
        name: '<PERSON>',
        age: 30,
        gender: 'male',
        familyStatus: 'married',
        location: Location(town: 'San Francisco', country: 'USA'),
        likes: ['reading', 'hiking', 'technology'],
        dislikes: ['loud noises', 'crowded places'],
        personalityTraits: ['analytical', 'introverted'],
        goals: [
          Goal(
            id: 'goal-1',
            description: 'Learn a new programming language',
            status: 'in_progress',
            createdAt: DateTime(2024, 1, 1),
          ),
        ],
        facts: [
          Fact(key: 'occupation', value: 'Software Engineer'),
          Fact(key: 'experience', value: '5 years'),
        ],
        family: [
          RelationInfo(
            name: 'Jane Doe',
            age: 28,
            relation: 'spouse',
            otherInfo: ['works in marketing'],
          ),
        ],
        preferences: {
          'communication_style': 'direct',
          'session_length': 'medium',
        },
        interactionHistory: InteractionHistory(
          lastUpdated: DateTime(2024, 1, 10),
          sources: [],
        ),
      );

      final prompt = await SystemPromptBuilder.buildChatWithPersonaSystemPrompt(
        userProfile: userProfile,
        conversationContext:
            'Career development coaching. Focus on technical skills. Provide actionable steps.',
      );

      // Check that basic sections are included (fallback behavior in test environment)
      expect(prompt, contains('AI Coach Role & Identity'));
      expect(prompt, contains('coaching assistant'));

      // In test environment, SystemPromptService is not initialized,
      // so it returns the default fallback prompt without user profile integration
    });

    test('handles empty or null user profile gracefully', () async {
      final prompt = await SystemPromptBuilder.buildChatWithPersonaSystemPrompt(
        userProfile: null,
      );

      expect(prompt, contains('AI Coach Role & Identity'));
      expect(prompt, isNot(contains('User Profile')));
    });

    test('handles user profile with minimal information', () async {
      final userProfile = UserProfile(
        userId: 'minimal-user',
        interactionHistory: InteractionHistory(
          lastUpdated: DateTime(2024, 1, 10),
          sources: [],
        ),
      );

      final prompt = await SystemPromptBuilder.buildChatWithPersonaSystemPrompt(
        userProfile: userProfile,
      );

      expect(prompt, contains('AI Coach Role & Identity'));
      expect(prompt, contains('coaching assistant'));
    });

    test('builds system prompt with PathStep topic guidance', () async {
      final pathStep = PathStep(
        pathId: 'test-path-123',
        stepNumber: 1,
        title: 'Identify Your Core Values',
        description:
            'Explore and define the fundamental values that guide your decisions and actions.',
        completionCriteria:
            'List 5-7 core values with brief explanations of why they matter to you.',
        reflectionPrompts: [
          'What experiences have shaped your values?',
          'Which values do you prioritize when making difficult decisions?',
          'How do your values align with your current lifestyle?',
        ],
        createdAt: DateTime(2024, 1, 1),
      );

      final prompt = await SystemPromptBuilder.buildChatWithPersonaSystemPrompt(
        pathStep: pathStep,
        conversationContext: 'Guided path step coaching session',
      );

      // Check that basic sections are included
      expect(prompt, contains('AI Coach Role & Identity'));
      expect(prompt, contains('coaching assistant'));

      // In test environment, the fallback prompt is used, but we can verify
      // that the method accepts the pathStep parameter without errors
    });

    test('builds system prompt without PathStep for general chat', () async {
      final prompt = await SystemPromptBuilder.buildChatWithPersonaSystemPrompt(
        pathStep: null,
        conversationContext: 'General coaching conversation',
      );

      // Check that basic sections are included
      expect(prompt, contains('AI Coach Role & Identity'));
      expect(prompt, contains('coaching assistant'));

      // Verify the method handles null pathStep gracefully
    });
  });
}
