import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart';

void main() {
  group('Fact Value Formatting Tests', () {
    test(
      'UserProfile withNormalizedFactValues converts all fact values to strings',
      () {
        final userProfile = UserProfile(
          userId: 'test-user',
          facts: [
            Fact(key: 'string_value', value: 'Hello World'),
            Fact(key: 'int_value', value: 42),
            Fact(key: 'double_value', value: 3.14),
            Fact(key: 'bool_true', value: true),
            Fact(key: 'bool_false', value: false),
            Fact(key: 'null_value', value: null),
          ],
          interactionHistory: InteractionHistory(
            lastUpdated: DateTime.now(),
            sources: null,
          ),
        );

        final normalizedProfile = userProfile.withNormalizedFactValues();

        expect(normalizedProfile.facts?.length, equals(6));
        expect(normalizedProfile.facts?[0].value, equals('Hello World'));
        expect(normalizedProfile.facts?[1].value, equals('42'));
        expect(normalizedProfile.facts?[2].value, equals('3.14'));
        expect(normalizedProfile.facts?[3].value, equals('true'));
        expect(normalizedProfile.facts?[4].value, equals('false'));
        expect(normalizedProfile.facts?[5].value, equals(''));
      },
    );

    test('UserProfile withNormalizedFactValues handles empty facts list', () {
      final userProfile = UserProfile(
        userId: 'test-user',
        facts: [],
        interactionHistory: InteractionHistory(
          lastUpdated: DateTime.now(),
          sources: null,
        ),
      );

      final normalizedProfile = userProfile.withNormalizedFactValues();

      expect(normalizedProfile.facts?.length, equals(0));
    });

    test('UserProfile withNormalizedFactValues handles null facts', () {
      final userProfile = UserProfile(
        userId: 'test-user',
        facts: null,
        interactionHistory: InteractionHistory(
          lastUpdated: DateTime.now(),
          sources: null,
        ),
      );

      final normalizedProfile = userProfile.withNormalizedFactValues();

      expect(normalizedProfile.facts, isNull);
    });

    test('UserProfile normalization handles complex types correctly', () {
      final userProfile = UserProfile(
        userId: 'test-user',
        facts: [
          Fact(key: 'list_value', value: [1, 2, 3]),
          Fact(key: 'map_value', value: {'key': 'value'}),
        ],
        interactionHistory: InteractionHistory(
          lastUpdated: DateTime.now(),
          sources: null,
        ),
      );

      final normalizedProfile = userProfile.withNormalizedFactValues();

      expect(normalizedProfile.facts?.length, equals(2));
      expect(normalizedProfile.facts?[0].value, equals('[1, 2, 3]'));
      expect(normalizedProfile.facts?[1].value, equals('{key: value}'));
    });

    test('Fact model can handle dynamic values', () {
      final stringFact = Fact(key: 'test', value: 'string');
      final intFact = Fact(key: 'test', value: 42);
      final boolFact = Fact(key: 'test', value: true);
      final nullFact = Fact(key: 'test', value: null);

      expect(stringFact.value, equals('string'));
      expect(intFact.value, equals(42));
      expect(boolFact.value, equals(true));
      expect(nullFact.value, isNull);
    });

    test('Fact JSON serialization preserves dynamic values', () {
      final fact = Fact(key: 'test_key', value: 123);
      final json = fact.toJson();
      final reconstructed = Fact.fromJson(json);

      expect(reconstructed.key, equals('test_key'));
      expect(reconstructed.value, equals(123));
    });
  });
}
