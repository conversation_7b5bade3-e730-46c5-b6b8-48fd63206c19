import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/widgets/youtube_video_resource_widget.dart';

void main() {
  group('YouTubeVideoResourceWidget', () {
    late models.ExternalResource testResource;

    setUp(() {
      testResource = models.ExternalResource(
        title: 'Test YouTube Video',
        link: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
        type: models.ExternalResourceType.video,
        description: 'A test video description',
        source: 'YouTube',
        durationMinutes: 5,
      );
    });

    testWidgets('displays resource title and metadata', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: YouTubeVideoResourceWidget(resource: testResource),
          ),
        ),
      );

      // Check if title is displayed
      expect(find.text('Test YouTube Video'), findsOneWidget);
      
      // Check if type badge is displayed
      expect(find.text('Video'), findsOneWidget);
      
      // Check if description is displayed
      expect(find.text('A test video description'), findsOneWidget);
      
      // Check if source is displayed
      expect(find.text('YouTube'), findsOneWidget);
      
      // Check if duration is displayed
      expect(find.text('5 min'), findsOneWidget);
    });

    testWidgets('shows web fallback on web platform', (WidgetTester tester) async {
      // This test simulates web behavior
      debugDefaultTargetPlatformOverride = TargetPlatform.linux; // Simulate web
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: YouTubeVideoResourceWidget(resource: testResource),
          ),
        ),
      );

      // On web, should show fallback UI
      if (kIsWeb) {
        expect(find.text('Watch on YouTube'), findsOneWidget);
        expect(find.text('Tap to open video in a new tab'), findsOneWidget);
      }
      
      debugDefaultTargetPlatformOverride = null;
    });

    testWidgets('handles invalid YouTube URL gracefully', (WidgetTester tester) async {
      final invalidResource = models.ExternalResource(
        title: 'Invalid Video',
        link: 'https://example.com/not-youtube',
        type: models.ExternalResourceType.video,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: YouTubeVideoResourceWidget(resource: invalidResource),
          ),
        ),
      );

      // Should show error state for invalid URL
      await tester.pump();
      expect(find.text('Invalid YouTube URL'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
    });

    testWidgets('displays play icon and video type badge', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: YouTubeVideoResourceWidget(resource: testResource),
          ),
        ),
      );

      // Check for play icon
      expect(find.byIcon(Icons.play_circle_outline), findsOneWidget);
      
      // Check for video type badge
      expect(find.text('Video'), findsOneWidget);
    });

    testWidgets('shows loading state initially', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: YouTubeVideoResourceWidget(resource: testResource),
          ),
        ),
      );

      // Should show loading or player depending on platform
      // On non-web platforms, should eventually show player or loading
      await tester.pump();
      
      // The widget should be rendered without throwing errors
      expect(find.byType(YouTubeVideoResourceWidget), findsOneWidget);
    });
  });
}
