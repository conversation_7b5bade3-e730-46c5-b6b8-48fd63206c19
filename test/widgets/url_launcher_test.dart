import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/widgets/generic_resource_widget.dart';

void main() {
  group('URL Launcher Integration', () {
    testWidgets('GenericResourceWidget handles URL tapping without errors', (WidgetTester tester) async {
      final testResource = models.ExternalResource(
        title: 'Test URL Resource',
        link: 'https://www.example.com',
        type: models.ExternalResourceType.article,
        description: 'A test resource for URL launching',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: GenericResourceWidget(resource: testResource),
          ),
        ),
      );

      // Find the tappable widget
      final inkWell = find.byType(InkWell);
      expect(inkWell, findsOneWidget);

      // Tap the widget - this should not throw any errors
      // Note: In test environment, the actual URL won't open, but we can verify
      // that the tap gesture is handled without exceptions
      await tester.tap(inkWell);
      await tester.pump();

      // Verify the widget is still rendered correctly after tap
      expect(find.text('Test URL Resource'), findsOneWidget);
      expect(find.text('Article'), findsOneWidget);
    });

    testWidgets('GenericResourceWidget handles malformed URLs gracefully', (WidgetTester tester) async {
      final malformedResource = models.ExternalResource(
        title: 'Malformed URL Resource',
        link: 'not-a-valid-url',
        type: models.ExternalResourceType.website,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: GenericResourceWidget(resource: malformedResource),
          ),
        ),
      );

      // Find the tappable widget
      final inkWell = find.byType(InkWell);
      expect(inkWell, findsOneWidget);

      // Tap the widget with malformed URL - should handle gracefully
      await tester.tap(inkWell);
      await tester.pump();

      // Verify the widget is still rendered correctly after tap
      expect(find.text('Malformed URL Resource'), findsOneWidget);
      expect(find.text('Website'), findsOneWidget);
    });

    testWidgets('GenericResourceWidget handles empty URLs gracefully', (WidgetTester tester) async {
      final emptyUrlResource = models.ExternalResource(
        title: 'Empty URL Resource',
        link: '',
        type: models.ExternalResourceType.other,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: GenericResourceWidget(resource: emptyUrlResource),
          ),
        ),
      );

      // Find the tappable widget
      final inkWell = find.byType(InkWell);
      expect(inkWell, findsOneWidget);

      // Tap the widget with empty URL - should handle gracefully
      await tester.tap(inkWell);
      await tester.pump();

      // Verify the widget is still rendered correctly after tap
      expect(find.text('Empty URL Resource'), findsOneWidget);
      expect(find.text('Other'), findsOneWidget);
    });

    testWidgets('GenericResourceWidget handles various URL schemes', (WidgetTester tester) async {
      final urlSchemes = [
        'https://www.example.com',
        'http://www.example.com',
        'mailto:<EMAIL>',
        'tel:+1234567890',
        'ftp://files.example.com',
      ];

      for (int i = 0; i < urlSchemes.length; i++) {
        final resource = models.ExternalResource(
          title: 'URL Scheme Test $i',
          link: urlSchemes[i],
          type: models.ExternalResourceType.other,
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: GenericResourceWidget(resource: resource),
            ),
          ),
        );

        // Find the tappable widget
        final inkWell = find.byType(InkWell);
        expect(inkWell, findsOneWidget);

        // Tap the widget - should handle different URL schemes gracefully
        await tester.tap(inkWell);
        await tester.pump();

        // Verify the widget is still rendered correctly after tap
        expect(find.text('URL Scheme Test $i'), findsOneWidget);
      }
    });
  });
}
