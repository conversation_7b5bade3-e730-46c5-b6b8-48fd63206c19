import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/widgets/generic_resource_widget.dart';

void main() {
  group('GenericResourceWidget', () {
    late models.ExternalResource testResource;

    setUp(() {
      testResource = models.ExternalResource(
        title: 'Test Article',
        link: 'https://www.example.com/article',
        type: models.ExternalResourceType.article,
        description: 'A test article description',
        source: 'Example.com',
        durationMinutes: 10,
      );
    });

    testWidgets('displays resource title and metadata', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: GenericResourceWidget(resource: testResource),
          ),
        ),
      );

      // Check if title is displayed
      expect(find.text('Test Article'), findsOneWidget);
      
      // Check if type badge is displayed
      expect(find.text('Article'), findsOneWidget);
      
      // Check if description is displayed
      expect(find.text('A test article description'), findsOneWidget);
      
      // Check if source is displayed
      expect(find.text('Example.com'), findsOneWidget);
      
      // Check if duration is displayed
      expect(find.text('10 min'), findsOneWidget);
    });

    testWidgets('displays correct icon for article type', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: GenericResourceWidget(resource: testResource),
          ),
        ),
      );

      // Check for article icon
      expect(find.byIcon(Icons.article), findsOneWidget);
      
      // Check for external link icon
      expect(find.byIcon(Icons.open_in_new), findsOneWidget);
    });

    testWidgets('displays correct icon for different resource types', (WidgetTester tester) async {
      final testCases = [
        (models.ExternalResourceType.book, Icons.menu_book),
        (models.ExternalResourceType.podcast, Icons.podcasts),
        (models.ExternalResourceType.tool, Icons.build),
        (models.ExternalResourceType.course, Icons.school),
        (models.ExternalResourceType.website, Icons.language),
        (models.ExternalResourceType.other, Icons.link),
      ];

      for (final (type, expectedIcon) in testCases) {
        final resource = models.ExternalResource(
          title: 'Test ${type.displayName}',
          link: 'https://www.example.com',
          type: type,
        );

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: GenericResourceWidget(resource: resource),
            ),
          ),
        );

        expect(find.byIcon(expectedIcon), findsOneWidget);
        expect(find.text(type.displayName), findsOneWidget);
      }
    });

    testWidgets('handles resource without optional fields', (WidgetTester tester) async {
      final minimalResource = models.ExternalResource(
        title: 'Minimal Resource',
        link: 'https://www.example.com',
        type: models.ExternalResourceType.website,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: GenericResourceWidget(resource: minimalResource),
          ),
        ),
      );

      // Check if title is displayed
      expect(find.text('Minimal Resource'), findsOneWidget);
      
      // Check if type badge is displayed
      expect(find.text('Website'), findsOneWidget);
      
      // Description, source, and duration should not be displayed
      expect(find.text('A test article description'), findsNothing);
      expect(find.text('Example.com'), findsNothing);
      expect(find.text('10 min'), findsNothing);
    });

    testWidgets('is tappable and shows proper styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: GenericResourceWidget(resource: testResource),
          ),
        ),
      );

      // Find the InkWell widget
      final inkWell = find.byType(InkWell);
      expect(inkWell, findsOneWidget);

      // Check that title has underline decoration
      final titleText = tester.widget<Text>(
        find.text('Test Article'),
      );
      expect(titleText.style?.decoration, TextDecoration.underline);
    });

    testWidgets('handles very long URLs gracefully', (WidgetTester tester) async {
      final longUrlResource = models.ExternalResource(
        title: 'Resource with Long URL',
        link: 'https://www.example.com/very/long/path/with/many/segments/and/query/parameters?param1=value1&param2=value2&param3=value3',
        type: models.ExternalResourceType.article,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: GenericResourceWidget(resource: longUrlResource),
          ),
        ),
      );

      // Widget should render without throwing errors
      expect(find.byType(GenericResourceWidget), findsOneWidget);
      expect(find.text('Resource with Long URL'), findsOneWidget);
    });
  });
}
