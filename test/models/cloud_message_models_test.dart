import 'package:flutter_test/flutter_test.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:upshift/models/cloud_message_models.dart';

import 'cloud_message_models_test.mocks.dart';

// Generate mocks for Firebase classes
@GenerateMocks([RemoteMessage, RemoteNotification])
void main() {
  group('EngagementData', () {
    test('should create from JSON correctly', () {
      // Arrange
      final json = {
        'title': 'Test Title',
        'description': 'Test Description',
        'imageUrl': 'https://example.com/image.png',
      };

      // Act
      final result = EngagementData.fromJson(json);

      // Assert
      expect(result.title, equals('Test Title'));
      expect(result.description, equals('Test Description'));
      expect(result.imageUrl, equals('https://example.com/image.png'));
    });

    test('should create from JSON without optional imageUrl', () {
      // Arrange
      final json = {'title': 'Test Title', 'description': 'Test Description'};

      // Act
      final result = EngagementData.fromJson(json);

      // Assert
      expect(result.title, equals('Test Title'));
      expect(result.description, equals('Test Description'));
      expect(result.imageUrl, isNull);
    });

    test('should throw when required fields are missing', () {
      // Arrange
      final json = {
        'title': 'Test Title',
        // Missing description
      };

      // Act & Assert
      expect(() => EngagementData.fromJson(json), throwsA(isA<TypeError>()));
    });
  });

  group('GoalsAndProgressData', () {
    test('should create from JSON correctly', () {
      // Arrange
      final json = {
        'goalName': 'Fitness Goal',
        'goalId': 'goal_123',
        'description': 'You are making great progress!',
        'progressPercentage': 75,
        'currentStep': 'Week 3 workout',
      };

      // Act
      final result = GoalsAndProgressData.fromJson(json);

      // Assert
      expect(result.goalName, equals('Fitness Goal'));
      expect(result.goalId, equals('goal_123'));
      expect(result.description, equals('You are making great progress!'));
      expect(result.progressPercentage, equals(75));
      expect(result.currentStep, equals('Week 3 workout'));
    });

    test('should handle invalid progress percentage', () {
      // Arrange
      final json = {
        'goalName': 'Fitness Goal',
        'goalId': 'goal_123',
        'description': 'You are making great progress!',
        'progressPercentage': 'invalid', // Invalid type
        'currentStep': 'Week 3 workout',
      };

      // Act & Assert
      expect(
        () => GoalsAndProgressData.fromJson(json),
        throwsA(isA<TypeError>()),
      );
    });
  });

  group('GuidedPathUpdatesData', () {
    test('should create from JSON correctly', () {
      // Arrange
      final json = {
        'pathId': 'path_123',
        'pathName': 'Meditation Journey',
        'stepId': 'step_456',
        'stepName': 'Breathing Exercise',
        'imageUrl': 'https://example.com/meditation.png',
      };

      // Act
      final result = GuidedPathUpdatesData.fromJson(json);

      // Assert
      expect(result.pathId, equals('path_123'));
      expect(result.pathName, equals('Meditation Journey'));
      expect(result.stepId, equals('step_456'));
      expect(result.stepName, equals('Breathing Exercise'));
      expect(result.imageUrl, equals('https://example.com/meditation.png'));
    });

    test('should create from JSON without optional imageUrl', () {
      // Arrange
      final json = {
        'pathId': 'path_123',
        'pathName': 'Meditation Journey',
        'stepId': 'step_456',
        'stepName': 'Breathing Exercise',
      };

      // Act
      final result = GuidedPathUpdatesData.fromJson(json);

      // Assert
      expect(result.pathId, equals('path_123'));
      expect(result.pathName, equals('Meditation Journey'));
      expect(result.stepId, equals('step_456'));
      expect(result.stepName, equals('Breathing Exercise'));
      expect(result.imageUrl, isNull);
    });
  });

  group('SystemAnnouncementsData', () {
    test('should create from JSON correctly', () {
      // Arrange
      final json = {
        'announcementId': 'announcement_123',
        'announcementTitle': 'New Feature!',
        'announcementBody': 'Check out our latest updates...',
        'imageUrl': 'https://example.com/announcement.png',
      };

      // Act
      final result = SystemAnnouncementsData.fromJson(json);

      // Assert
      expect(result.announcementId, equals('announcement_123'));
      expect(result.announcementTitle, equals('New Feature!'));
      expect(
        result.announcementBody,
        equals('Check out our latest updates...'),
      );
      expect(result.imageUrl, equals('https://example.com/announcement.png'));
    });
  });

  group('WeeklyInsightsData', () {
    test('should create from JSON correctly', () {
      // Arrange
      final json = {
        'insightId': 'insight_123',
        'insightTitle': 'Your Weekly Progress',
        'insightBody': 'Here is what you achieved this week...',
        'imageUrl': 'https://example.com/insight.png',
      };

      // Act
      final result = WeeklyInsightsData.fromJson(json);

      // Assert
      expect(result.insightId, equals('insight_123'));
      expect(result.insightTitle, equals('Your Weekly Progress'));
      expect(
        result.insightBody,
        equals('Here is what you achieved this week...'),
      );
      expect(result.imageUrl, equals('https://example.com/insight.png'));
    });
  });

  group('PushNotificationData', () {
    test('should create engagement notification from message type', () {
      // Arrange
      final data = {'title': 'Test Title', 'description': 'Test Description'};

      // Act
      final result = PushNotificationData.fromMessageType('engagement', data);

      // Assert
      expect(result, isA<EngagementData>());
      final engagementData = result as EngagementData;
      expect(engagementData.title, equals('Test Title'));
      expect(engagementData.description, equals('Test Description'));
    });

    test('should throw for unknown message type', () {
      // Arrange
      final data = {'title': 'Test'};

      // Act & Assert
      expect(
        () => PushNotificationData.fromMessageType('unknown_type', data),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('should create from RemoteMessage correctly', () {
      // Arrange
      final mockMessage = MockRemoteMessage();
      when(mockMessage.data).thenReturn({
        'messageType': 'engagement',
        'title': 'Test Title',
        'description': 'Test Description',
      });

      // Act
      final result = PushNotificationData.fromRemoteMessage(mockMessage);

      // Assert
      expect(result, isA<PushNotificationData>());
    });

    test('should throw when RemoteMessage has no messageType', () {
      // Arrange
      final mockMessage = MockRemoteMessage();
      when(
        mockMessage.data,
      ).thenReturn({'title': 'Test Title', 'description': 'Test Description'});

      // Act & Assert
      expect(
        () => PushNotificationData.fromRemoteMessage(mockMessage),
        throwsA(isA<ArgumentError>()),
      );
    });
  });

  group('CloudMessage', () {
    test('should create from RemoteMessage correctly', () {
      // Arrange
      final mockMessage = MockRemoteMessage();
      final mockNotification = MockRemoteNotification();

      when(mockMessage.data).thenReturn({
        'messageType': 'engagement',
        'title': 'Test Title',
        'description': 'Test Description',
        'extraData': 'extra_value',
      });
      when(mockMessage.notification).thenReturn(mockNotification);

      // Act
      final result = CloudMessage.fromRemoteMessage(mockMessage);

      // Assert
      expect(result.messageType, equals('engagement'));
      expect(result.data, isA<PushNotificationData>());
      expect(result.additionalData, isNotNull);
      expect(result.additionalData!['extraData'], equals('extra_value'));
    });

    test('should throw when RemoteMessage has no messageType', () {
      // Arrange
      final mockMessage = MockRemoteMessage();
      when(
        mockMessage.data,
      ).thenReturn({'title': 'Test Title', 'description': 'Test Description'});

      // Act & Assert
      expect(
        () => CloudMessage.fromRemoteMessage(mockMessage),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('should handle RemoteMessage with no additional data', () {
      // Arrange
      final mockMessage = MockRemoteMessage();
      when(mockMessage.data).thenReturn({
        'messageType': 'engagement',
        'title': 'Test Title',
        'description': 'Test Description',
      });
      when(mockMessage.notification).thenReturn(null);

      // Act
      final result = CloudMessage.fromRemoteMessage(mockMessage);

      // Assert
      expect(result.messageType, equals('engagement'));
      expect(result.additionalData, isNull);
    });
  });

  group('Edge Cases and Error Handling', () {
    test('should handle empty JSON data', () {
      // Arrange
      final json = <String, dynamic>{};

      // Act & Assert
      expect(() => EngagementData.fromJson(json), throwsA(isA<TypeError>()));
    });

    test('should handle null values in JSON', () {
      // Arrange
      final json = {'title': null, 'description': 'Test Description'};

      // Act & Assert
      expect(() => EngagementData.fromJson(json), throwsA(isA<TypeError>()));
    });

    test('should handle malformed JSON for goals and progress', () {
      // Arrange
      final json = {
        'goalName': 'Test Goal',
        'goalId': 'goal_123',
        'description': 'Test Description',
        'progressPercentage': -10, // Invalid percentage
        'currentStep': 'Step 1',
      };

      // Act
      final result = GoalsAndProgressData.fromJson(json);

      // Assert - Should still create object even with invalid percentage
      expect(result.progressPercentage, equals(-10));
    });

    test('should handle very long strings', () {
      // Arrange
      final longString = 'A' * 10000;
      final json = {'title': longString, 'description': longString};

      // Act
      final result = EngagementData.fromJson(json);

      // Assert
      expect(result.title, equals(longString));
      expect(result.description, equals(longString));
    });

    test('should handle special characters in strings', () {
      // Arrange
      final json = {
        'title': 'Test 🎉 Title with émojis and ñ',
        'description': 'Description with "quotes" and \'apostrophes\'',
      };

      // Act
      final result = EngagementData.fromJson(json);

      // Assert
      expect(result.title, equals('Test 🎉 Title with émojis and ñ'));
      expect(
        result.description,
        equals('Description with "quotes" and \'apostrophes\''),
      );
    });
  });
}
